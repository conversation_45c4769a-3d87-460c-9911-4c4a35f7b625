// 游戏数据管理
class GameDataManager {
    constructor() {
        this.storageKey = 'mahjongGameData';
        this.currentGameKey = 'currentGame';
        this.isInitialized = false;
        this.initPromise = this.init();
    }

    // 初始化数据
    async init() {
        try {
            console.log('=== GameDataManager 初始化开始 ===');

            // 检查localStorage可用性
            if (typeof Storage === 'undefined') {
                throw new Error('localStorage不支持');
            }

            const data = this.getGameData();
            if (!data) {
                console.log('没有找到游戏数据，初始化默认数据...');
                await this.initDefaultData();
            } else {
                console.log('找到现有游戏数据，验证数据完整性...');
                await this.validateAndFixData(data);
            }

            this.isInitialized = true;
            console.log('=== GameDataManager 初始化完成 ===');

        } catch (error) {
            console.error('GameDataManager 初始化失败:', error);
            this.isInitialized = false;
            throw error;
        }
    }

    // 确保初始化完成
    async ensureInitialized() {
        if (!this.isInitialized) {
            await this.initPromise;
        }
        return this.isInitialized;
    }

    // 验证和修复数据
    async validateAndFixData(data) {
        let needsSave = false;

        // 确保games对象存在
        if (!data.games || typeof data.games !== 'object') {
            console.log('修复games对象...');
            data.games = {};
            needsSave = true;
        }

        // 检查currentGameId的有效性
        if (data.currentGameId && !data.games[data.currentGameId]) {
            console.log('清除无效的currentGameId...');
            data.currentGameId = null;
            needsSave = true;
        }

        // 验证每个游戏的数据完整性
        Object.values(data.games).forEach(game => {
            if (!game.rounds) {
                game.rounds = [];
                needsSave = true;
            }
            if (!game.players) {
                game.players = [];
                needsSave = true;
            }
        });

        if (needsSave) {
            console.log('保存修复后的数据...');
            this.saveGameData(data);
        }
    }

    // 初始化默认数据
    async initDefaultData() {
        try {
            console.log('创建默认游戏数据...');

            const defaultData = {
                games: {
                    'game_001': {
                        id: 'game_001',
                        name: '周末家庭局',
                        type: '广东麻将',
                        location: '家里客厅',
                        startTime: new Date().toISOString(),
                        status: 'ongoing',
                        players: [
                            { id: 'p1', name: '张三', avatar: '张', totalScore: 0 },
                            { id: 'p2', name: '李四', avatar: '李', totalScore: 0 },
                            { id: 'p3', name: '王五', avatar: '王', totalScore: 0 },
                            { id: 'p4', name: '赵六', avatar: '赵', totalScore: 0 }
                        ],
                        rounds: []
                    }
                },
                currentGameId: 'game_001'
            };

            this.saveGameData(defaultData);
            console.log('默认数据创建成功');

        } catch (error) {
            console.error('创建默认数据失败:', error);
            throw error;
        }
    }

    // 获取所有游戏数据
    getGameData() {
        try {
            // 检查localStorage是否可用
            if (typeof Storage === 'undefined') {
                console.error('localStorage不支持');
                return null;
            }

            const data = localStorage.getItem(this.storageKey);
            if (!data) {
                console.log('localStorage中没有游戏数据');
                return null;
            }

            const parsedData = JSON.parse(data);

            // 验证数据结构
            if (!parsedData || typeof parsedData !== 'object') {
                console.error('游戏数据格式无效');
                return null;
            }

            if (!parsedData.games || typeof parsedData.games !== 'object') {
                console.error('games对象无效，尝试修复...');
                parsedData.games = {};
            }

            if (typeof parsedData.currentGameId === 'undefined') {
                console.log('currentGameId未设置，设为null');
                parsedData.currentGameId = null;
            }

            console.log('游戏数据获取成功:', {
                gamesCount: Object.keys(parsedData.games).length,
                currentGameId: parsedData.currentGameId,
                dataSize: data.length
            });

            return parsedData;

        } catch (error) {
            console.error('获取游戏数据失败:', error);

            // 如果是JSON解析错误，尝试恢复
            if (error instanceof SyntaxError) {
                console.log('JSON解析失败，尝试清除损坏的数据...');
                try {
                    localStorage.removeItem(this.storageKey);
                    console.log('已清除损坏的数据');
                } catch (clearError) {
                    console.error('清除数据失败:', clearError);
                }
            }

            return null;
        }
    }

    // 保存游戏数据
    saveGameData(data) {
        try {
            // 检查localStorage是否可用
            if (typeof Storage === 'undefined') {
                throw new Error('localStorage不支持');
            }

            // 检查数据是否有效
            if (!data || typeof data !== 'object') {
                throw new Error('无效的数据格式');
            }

            const jsonString = JSON.stringify(data);

            // 检查数据大小（localStorage通常有5MB限制）
            if (jsonString.length > 5 * 1024 * 1024) {
                throw new Error('数据过大，超过localStorage限制');
            }

            localStorage.setItem(this.storageKey, jsonString);
            console.log('Game data saved to localStorage successfully', {
                dataSize: jsonString.length,
                gamesCount: Object.keys(data.games || {}).length
            });

            // 验证保存是否成功
            const savedData = localStorage.getItem(this.storageKey);
            if (!savedData) {
                throw new Error('数据保存后无法读取');
            }

            return true;
        } catch (error) {
            console.error('保存游戏数据失败:', error);

            // 尝试清理localStorage并重试
            if (error.name === 'QuotaExceededError' || error.message.includes('quota')) {
                console.log('尝试清理localStorage并重试...');
                try {
                    // 清理其他可能的数据
                    const keys = Object.keys(localStorage);
                    keys.forEach(key => {
                        if (key !== this.storageKey && key !== 'mahjong_friends') {
                            localStorage.removeItem(key);
                        }
                    });

                    // 重试保存
                    localStorage.setItem(this.storageKey, JSON.stringify(data));
                    console.log('清理后重试保存成功');
                    return true;
                } catch (retryError) {
                    console.error('重试保存也失败:', retryError);
                    throw retryError;
                }
            }

            throw error;
        }
    }

    // 获取当前游戏
    getCurrentGame() {
        const data = this.getGameData();
        if (!data) {
            console.warn('getCurrentGame: 无法获取游戏数据');
            return null;
        }

        if (!data.currentGameId) {
            console.warn('getCurrentGame: 没有设置当前游戏ID');
            return null;
        }

        if (!data.games || !data.games[data.currentGameId]) {
            console.warn('getCurrentGame: 当前游戏不存在', {
                currentGameId: data.currentGameId,
                availableGames: Object.keys(data.games || {})
            });
            return null;
        }

        const game = data.games[data.currentGameId];
        console.log('getCurrentGame: 成功获取当前游戏', {
            gameId: game.id,
            status: game.status,
            rounds: game.rounds?.length || 0
        });

        return game;
    }

    // 设置当前游戏
    setCurrentGame(gameId) {
        const data = this.getGameData();
        if (data && data.games[gameId]) {
            data.currentGameId = gameId;
            this.saveGameData(data);
            return true;
        }
        return false;
    }

    // 添加新局次
    addRound(roundData) {
        const data = this.getGameData();

        if (!data || !data.currentGameId || !data.games[data.currentGameId]) {
            console.error('No current game found');
            return false;
        }

        // 直接从data中获取当前游戏，确保是同一个对象引用
        const currentGame = data.games[data.currentGameId];

        // 创建新局次
        const newRound = {
            id: `round_${Date.now()}`,
            roundNumber: currentGame.rounds.length + 1,
            timestamp: new Date().toISOString(),
            winner: roundData.winner,
            roundType: roundData.roundType || '自摸',
            scores: roundData.scores,
            notes: roundData.notes || ''
        };

        console.log('创建新局次:', newRound);
        console.log('添加前的局次数量:', currentGame.rounds.length);

        // 添加到游戏中
        currentGame.rounds.push(newRound);

        console.log('添加后的局次数量:', currentGame.rounds.length);

        // 更新玩家总分
        this.updatePlayerScores(currentGame, roundData.scores);

        console.log('更新后的游戏数据:', currentGame);

        // 保存数据
        this.saveGameData(data);

        // 触发数据更新事件
        this.triggerDataUpdate();

        return true;
    }

    // 更新玩家总分
    updatePlayerScores(game, roundScores) {
        game.players.forEach(player => {
            // 直接使用玩家姓名作为键名，与保存时保持一致
            if (roundScores[player.name] !== undefined) {
                player.totalScore += roundScores[player.name];
                console.log(`更新玩家 ${player.name} 分数: +${roundScores[player.name]}, 总分: ${player.totalScore}`);
            } else {
                console.warn(`未找到玩家 ${player.name} 的分数数据，可用键名:`, Object.keys(roundScores));
            }
        });
    }

    // 获取玩家分数键名
    getPlayerScoreKey(playerName) {
        const keyMap = {
            '张三': 'zhang',
            '李四': 'li',
            '王五': 'wang',
            '赵六': 'zhao'
        };
        return keyMap[playerName] || playerName.toLowerCase();
    }

    // 根据键名获取玩家名称
    getPlayerNameByKey(key) {
        const nameMap = {
            'zhang': '张三',
            'li': '李四',
            'wang': '王五',
            'zhao': '赵六'
        };
        return nameMap[key] || key;
    }

    // 获取游戏统计
    getGameStats(gameId = null) {
        const game = gameId ? this.getGameData()?.games[gameId] : this.getCurrentGame();
        if (!game) return null;

        const stats = {
            totalRounds: game.rounds.length,
            duration: this.calculateDuration(game.startTime),
            playerStats: {}
        };

        // 计算每个玩家的统计
        game.players.forEach(player => {
            const playerRounds = game.rounds.filter(round => round.winner === player.name);
            stats.playerStats[player.id] = {
                name: player.name,
                totalScore: player.totalScore,
                wins: playerRounds.length,
                winRate: game.rounds.length > 0 ? (playerRounds.length / game.rounds.length * 100).toFixed(1) : 0
            };
        });

        return stats;
    }

    // 计算游戏时长
    calculateDuration(startTime) {
        const start = new Date(startTime);
        const now = new Date();
        const diffMs = now - start;
        const hours = Math.floor(diffMs / (1000 * 60 * 60));
        const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
        return `${hours}h ${minutes}m`;
    }

    // 结束游戏
    async endGame(gameId = null) {
        console.log('=== 开始结束游戏操作 ===');

        // 声明data变量在函数作用域
        let data = null;

        try {
            // 确保初始化完成
            console.log('检查初始化状态...');
            const isReady = await this.ensureInitialized();
            if (!isReady) {
                const error = new Error('GameDataManager未正确初始化');
                console.error(error.message);
                throw error;
            }
            console.log('✅ 初始化检查通过');

            // 获取游戏数据
            console.log('获取游戏数据...');
            data = this.getGameData();
            if (!data) {
                const error = new Error('无法获取游戏数据');
                console.error(error.message);
                throw error;
            }
            console.log('✅ 游戏数据获取成功');

        } catch (error) {
            console.error('结束游戏前检查失败:', error);
            // 尝试重新初始化
            try {
                console.log('尝试重新初始化...');
                await this.init();
                data = this.getGameData();
                if (!data) {
                    throw new Error('重新初始化后仍无法获取数据');
                }
                console.log('✅ 重新初始化成功，继续执行');
            } catch (retryError) {
                console.error('重新初始化也失败:', retryError);
                return false;
            }
        }

        console.log('游戏数据获取成功:', {
            gamesCount: Object.keys(data.games || {}).length,
            currentGameId: data.currentGameId
        });

        // 获取要结束的游戏
        let game = null;
        try {
            if (gameId) {
                // 如果指定了gameId，直接获取
                game = data.games[gameId];
                console.log('通过gameId获取游戏:', gameId, game ? '成功' : '失败');
                if (!game) {
                    throw new Error(`指定的游戏ID不存在: ${gameId}`);
                }
            } else {
                // 如果没有指定gameId，获取当前游戏
                if (!data.currentGameId) {
                    throw new Error('没有设置当前游戏ID');
                }
                game = data.games[data.currentGameId];
                console.log('通过currentGameId获取游戏:', data.currentGameId, game ? '成功' : '失败');
                if (!game) {
                    throw new Error(`当前游戏ID指向的游戏不存在: ${data.currentGameId}`);
                }
            }
        } catch (error) {
            console.error('获取游戏失败:', error.message, {
                gameId: gameId,
                currentGameId: data.currentGameId,
                availableGames: Object.keys(data.games || {})
            });
            return false;
        }

        console.log('结束游戏前状态:', {
            gameId: game.id,
            status: game.status,
            rounds: game.rounds?.length || 0
        });

        // 检查游戏是否已经结束
        if (game.status === 'completed') {
            console.log('游戏已经结束，无需重复操作');
            return true;
        }

        // 更新游戏状态
        try {
            console.log('更新游戏状态...');
            game.status = 'completed';
            game.endTime = new Date().toISOString();

            console.log('✅ 游戏状态更新成功:', {
                status: game.status,
                endTime: game.endTime
            });
        } catch (error) {
            console.error('更新游戏状态失败:', error);
            return false;
        }

        // 保存数据
        try {
            console.log('保存游戏数据...');
            const saveResult = this.saveGameData(data);
            if (!saveResult) {
                throw new Error('saveGameData返回false');
            }
            console.log('✅ 数据保存成功');
        } catch (error) {
            console.error('数据保存失败:', error);
            // 尝试恢复游戏状态
            try {
                game.status = 'ongoing';
                delete game.endTime;
                console.log('已恢复游戏状态');
            } catch (restoreError) {
                console.error('恢复游戏状态也失败:', restoreError);
            }
            return false;
        }

        // 验证数据是否正确保存
        try {
            console.log('验证数据保存...');
            const savedData = this.getGameData();
            if (!savedData || !savedData.games) {
                throw new Error('保存后无法获取数据');
            }

            const savedGame = savedData.games[game.id];
            if (!savedGame) {
                throw new Error(`保存后找不到游戏: ${game.id}`);
            }

            if (savedGame.status !== 'completed') {
                throw new Error(`游戏状态未正确保存: ${savedGame.status}`);
            }

            if (!savedGame.endTime) {
                throw new Error('游戏结束时间未正确保存');
            }

            console.log('✅ 数据保存验证成功:', {
                gameId: savedGame.id,
                status: savedGame.status,
                endTime: savedGame.endTime
            });

        } catch (error) {
            console.error('数据保存验证失败:', error.message);
            return false;
        }

        // 触发数据更新事件
        try {
            console.log('触发数据更新事件...');
            this.triggerDataUpdate();
            console.log('✅ 数据更新事件触发成功');
        } catch (error) {
            console.error('触发数据更新事件失败:', error);
            // 事件触发失败不影响游戏结束的成功
        }

        console.log('=== 游戏结束操作完成 ===');
        return true;
    }

    // 删除局次
    deleteRound(roundId) {
        const data = this.getGameData();
        const currentGame = this.getCurrentGame();
        
        if (!currentGame) return false;

        const roundIndex = currentGame.rounds.findIndex(round => round.id === roundId);
        if (roundIndex === -1) return false;

        // 移除局次
        const removedRound = currentGame.rounds.splice(roundIndex, 1)[0];

        // 重新计算所有玩家分数
        this.recalculatePlayerScores(currentGame);

        // 更新局次编号
        currentGame.rounds.forEach((round, index) => {
            round.roundNumber = index + 1;
        });

        this.saveGameData(data);
        this.triggerDataUpdate();
        return true;
    }

    // 重新计算玩家分数
    recalculatePlayerScores(game) {
        // 重置所有玩家分数
        game.players.forEach(player => {
            player.totalScore = 0;
        });

        // 重新累加所有局次分数
        game.rounds.forEach(round => {
            this.updatePlayerScores(game, round.scores);
        });
    }

    // 清除所有数据
    clearAllData() {
        localStorage.removeItem(this.storageKey);
        this.init();
    }

    // 导入数据（保留用于数据恢复）
    importData(data) {
        try {
            this.saveGameData(data);
            this.triggerDataUpdate();
            return true;
        } catch (error) {
            console.error('Import failed:', error);
            return false;
        }
    }

    // 触发数据更新事件
    triggerDataUpdate() {
        console.log('=== 触发数据更新事件 ===');

        try {
            // 获取当前数据状态用于调试
            const currentData = this.getGameData();
            console.log('当前数据状态:', {
                gamesCount: currentData ? Object.keys(currentData.games || {}).length : 0,
                currentGameId: currentData?.currentGameId,
                timestamp: Date.now()
            });

            // 触发自定义事件
            try {
                const event = new CustomEvent('gameDataUpdated', {
                    detail: {
                        timestamp: Date.now(),
                        source: 'gameDataManager',
                        gamesCount: currentData ? Object.keys(currentData.games || {}).length : 0
                    }
                });
                window.dispatchEvent(event);
                console.log('✅ gameDataUpdated事件已触发');
            } catch (eventError) {
                console.error('触发gameDataUpdated事件失败:', eventError);
            }

            // 同时触发storage事件（用于跨页面通信）
            try {
                const storageEvent = new StorageEvent('storage', {
                    key: this.storageKey,
                    newValue: localStorage.getItem(this.storageKey),
                    url: window.location.href
                });
                window.dispatchEvent(storageEvent);
                console.log('✅ storage事件已触发');
            } catch (storageError) {
                console.error('触发storage事件失败:', storageError);
            }

            // 额外触发一个通用的数据变化事件
            try {
                const dataChangeEvent = new CustomEvent('mahjongDataChanged', {
                    detail: {
                        timestamp: Date.now(),
                        type: 'gameDataUpdate'
                    }
                });
                window.dispatchEvent(dataChangeEvent);
                console.log('✅ mahjongDataChanged事件已触发');
            } catch (dataChangeError) {
                console.error('触发mahjongDataChanged事件失败:', dataChangeError);
            }

        } catch (error) {
            console.error('触发数据更新事件时发生严重错误:', error);
            throw error; // 重新抛出错误，让调用者知道事件触发失败
        }
    }

    // 验证数据一致性
    validateDataConsistency() {
        console.log('=== 验证数据一致性 ===');

        try {
            const data = this.getGameData();
            if (!data) {
                console.warn('没有找到游戏数据');
                return false;
            }

            // 检查数据结构
            if (!data.games || typeof data.games !== 'object') {
                console.error('游戏数据结构异常');
                return false;
            }

            // 检查当前游戏ID
            if (data.currentGameId && !data.games[data.currentGameId]) {
                console.warn('当前游戏ID指向不存在的游戏:', data.currentGameId);
            }

            // 检查每个游戏的数据完整性
            let validGames = 0;
            let completedGames = 0;

            Object.values(data.games).forEach(game => {
                if (game.id && game.name && game.players && Array.isArray(game.rounds)) {
                    validGames++;
                    if (game.status === 'completed') {
                        completedGames++;
                    }
                } else {
                    console.warn('发现不完整的游戏数据:', game.id);
                }
            });

            console.log('数据一致性检查结果:', {
                totalGames: Object.keys(data.games).length,
                validGames: validGames,
                completedGames: completedGames,
                currentGameId: data.currentGameId
            });

            return true;
        } catch (error) {
            console.error('数据一致性检查时发生错误:', error);
            return false;
        }
    }

    // 设置当前游戏
    setCurrentGame(gameId) {
        const allData = this.getGameData() || { games: {}, currentGameId: null };
        allData.currentGameId = gameId;
        this.saveGameData(allData);

        // 验证设置是否成功
        const verifyData = this.getGameData();
        if (verifyData.currentGameId === gameId) {
            console.log('当前游戏设置成功:', gameId);
        } else {
            console.error('当前游戏设置失败:', gameId);
        }
    }

    // 获取玩家统计
    getPlayerStats(playerName) {
        const gameData = this.getGameData();
        if (!gameData || !gameData.games) return null;

        const games = Object.values(gameData.games);
        let totalGames = 0;
        let totalRounds = 0;
        let totalWins = 0;
        let totalScore = 0;

        games.forEach(game => {
            const player = game.players.find(p => p.name === playerName);
            if (player) {
                totalGames++;
                totalRounds += game.rounds.length;
                totalScore += player.totalScore;

                game.rounds.forEach(round => {
                    if (round.winner === playerName) {
                        totalWins++;
                    }
                });
            }
        });

        return {
            playerName,
            totalGames,
            totalRounds,
            totalWins,
            totalScore,
            winRate: totalRounds > 0 ? Math.round((totalWins / totalRounds) * 100) : 0,
            avgScore: totalGames > 0 ? Math.round(totalScore / totalGames) : 0
        };
    }

    // 获取全局统计
    getGlobalStats() {
        const gameData = this.getGameData();
        if (!gameData || !gameData.games) {
            return {
                totalGames: 0,
                totalRounds: 0,
                totalWins: 0,
                winRate: 0,
                avgScore: 0
            };
        }

        const games = Object.values(gameData.games);
        let totalGames = games.length;
        let totalRounds = 0;
        let totalWins = 0;
        let totalScore = 0;

        games.forEach(game => {
            totalRounds += game.rounds.length;
            const currentPlayer = game.players[0]; // 假设当前用户是第一个玩家
            if (currentPlayer) {
                totalScore += currentPlayer.totalScore;
                game.rounds.forEach(round => {
                    if (round.winner === currentPlayer.name) {
                        totalWins++;
                    }
                });
            }
        });

        return {
            totalGames,
            totalRounds,
            totalWins,
            winRate: totalRounds > 0 ? Math.round((totalWins / totalRounds) * 100) : 0,
            avgScore: totalGames > 0 ? Math.round(totalScore / totalGames) : 0
        };
    }
}

// 创建全局实例
const gameDataManager = new GameDataManager();

// 导出到全局
window.gameDataManager = gameDataManager;
