/* iOS基础样式 - 麻将码App */

/* iPhone 15 尺寸设置 */
:root {
  --device-width: 393px;
  --device-height: 852px;
  --status-bar-height: 54px;
  --nav-bar-height: 44px;
  --home-indicator-height: 34px;
  --safe-area-top: 54px;
  --safe-area-bottom: 34px;
  
  /* 麻将主题色彩 */
  --primary-color: #2E7D32; /* 深绿色 - 麻将桌色 */
  --secondary-color: #4CAF50; /* 亮绿色 */
  --accent-color: #FF6B35; /* 橙红色 - 红中色 */
  --background-color: #F5F5F5;
  --card-background: #FFFFFF;
  --text-primary: #212121;
  --text-secondary: #757575;
  --border-color: #E0E0E0;
  
  /* iOS系统色彩 */
  --ios-blue: #007AFF;
  --ios-red: #FF3B30;
  --ios-green: #34C759;
  --ios-orange: #FF9500;
  --ios-gray: #8E8E93;
}

/* 设备容器 */
.device-container {
  width: var(--device-width);
  height: var(--device-height);
  margin: 20px auto;
  background: #000;
  border-radius: 40px;
  padding: 8px;
  box-shadow: 0 20px 40px rgba(0,0,0,0.3);
  position: relative;
  overflow: hidden;
}

/* 屏幕内容区域 */
.screen {
  width: 100%;
  height: 100%;
  background: var(--background-color);
  border-radius: 32px;
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
}

/* 状态栏 */
.status-bar {
  height: var(--status-bar-height);
  background: transparent;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
  position: relative;
  z-index: 100;
}

.status-left {
  display: flex;
  align-items: center;
  gap: 4px;
}

.status-right {
  display: flex;
  align-items: center;
  gap: 4px;
}

.battery {
  width: 24px;
  height: 12px;
  border: 1px solid var(--text-primary);
  border-radius: 2px;
  position: relative;
}

.battery::after {
  content: '';
  position: absolute;
  right: -3px;
  top: 3px;
  width: 2px;
  height: 6px;
  background: var(--text-primary);
  border-radius: 0 1px 1px 0;
}

.battery-fill {
  height: 100%;
  background: var(--ios-green);
  border-radius: 1px;
  width: 80%;
}

/* 导航栏 */
.nav-bar {
  height: var(--nav-bar-height);
  background: var(--card-background);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  border-bottom: 1px solid var(--border-color);
  position: relative;
  z-index: 99;
}

.nav-title {
  font-size: 17px;
  font-weight: 600;
  color: var(--text-primary);
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
}

.nav-button {
  background: none;
  border: none;
  color: var(--ios-blue);
  font-size: 17px;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: background-color 0.2s;
}

.nav-button:hover {
  background: rgba(0,122,255,0.1);
}

.nav-button.disabled {
  color: var(--ios-gray);
  cursor: not-allowed;
}

/* 安全区域内容 */
.safe-area {
  flex: 1;
  overflow-y: auto;
  padding: 0;
  background: var(--background-color);
}

/* Home指示器 */
.home-indicator {
  height: var(--home-indicator-height);
  display: flex;
  justify-content: center;
  align-items: center;
  background: transparent;
}

.home-indicator-bar {
  width: 134px;
  height: 5px;
  background: rgba(0,0,0,0.3);
  border-radius: 3px;
}

/* 通用卡片样式 */
.card {
  background: var(--card-background);
  border-radius: 12px;
  padding: 16px;
  margin: 8px 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  border: 1px solid var(--border-color);
}

/* 按钮样式 */
.btn-primary {
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: 12px;
  padding: 16px 24px;
  font-size: 17px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  width: 100%;
  margin: 8px 0;
}

.btn-primary:hover {
  background: #1B5E20;
  transform: translateY(-1px);
}

.btn-secondary {
  background: transparent;
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
  border-radius: 12px;
  padding: 14px 24px;
  font-size: 17px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  width: 100%;
  margin: 8px 0;
}

.btn-secondary:hover {
  background: var(--primary-color);
  color: white;
}

/* 麻将主题装饰 */
.mahjong-bg {
  background: linear-gradient(135deg, #2E7D32 0%, #4CAF50 100%);
  position: relative;
  overflow: hidden;
}

.mahjong-bg::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><text y="50" font-size="40" fill="rgba(255,255,255,0.1)">🀄</text></svg>') repeat;
  background-size: 60px 60px;
  animation: float 20s linear infinite;
}

@keyframes float {
  0% { transform: translate(0, 0) rotate(0deg); }
  100% { transform: translate(-50px, -50px) rotate(360deg); }
}

/* 响应式调整 */
@media (max-width: 400px) {
  .device-container {
    width: 100%;
    height: 100vh;
    margin: 0;
    border-radius: 0;
    padding: 0;
  }
  
  .screen {
    border-radius: 0;
  }
}

/* 自定义弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  backdrop-filter: blur(5px);
}

.modal-overlay.show {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  background: white;
  border-radius: 16px;
  padding: 24px;
  margin: 20px;
  max-width: 320px;
  width: 100%;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  transform: scale(0.8) translateY(20px);
  transition: all 0.3s ease;
}

.modal-overlay.show .modal-content {
  transform: scale(1) translateY(0);
}

.modal-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 12px;
  text-align: center;
}

.modal-message {
  font-size: 16px;
  color: var(--text-secondary);
  line-height: 1.4;
  text-align: center;
  margin-bottom: 24px;
}

.modal-input {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid var(--border-color);
  border-radius: 12px;
  font-size: 16px;
  margin-bottom: 24px;
  transition: border-color 0.2s;
  box-sizing: border-box;
}

.modal-input:focus {
  outline: none;
  border-color: var(--primary-color);
}

.modal-buttons {
  display: flex;
  gap: 12px;
}

.modal-button {
  flex: 1;
  padding: 12px 20px;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
}

.modal-button.primary {
  background: var(--primary-color);
  color: white;
}

.modal-button.primary:hover {
  background: #1B5E20;
}

.modal-button.secondary {
  background: var(--background-color);
  color: var(--text-secondary);
}

.modal-button.secondary:hover {
  background: var(--border-color);
}

.modal-button.danger {
  background: var(--ios-red);
  color: white;
}

.modal-button.danger:hover {
  background: #D32F2F;
}

/* Toast 提示样式 */
.toast {
  position: fixed;
  top: 120px;
  left: 50%;
  transform: translateX(-50%) translateY(-20px);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 12px 20px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  z-index: 1001;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  max-width: 280px;
  text-align: center;
}

.toast.show {
  opacity: 1;
  visibility: visible;
  transform: translateX(-50%) translateY(0);
}

.toast.success {
  background: rgba(76, 175, 80, 0.9);
}

.toast.error {
  background: rgba(244, 67, 54, 0.9);
}

.toast.warning {
  background: rgba(255, 152, 0, 0.9);
}

/* 工具类 */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }
.mb-1 { margin-bottom: 8px; }
.mb-2 { margin-bottom: 16px; }
.mb-3 { margin-bottom: 24px; }
.mt-1 { margin-top: 8px; }
.mt-2 { margin-top: 16px; }
.mt-3 { margin-top: 24px; }
.p-1 { padding: 8px; }
.p-2 { padding: 16px; }
.p-3 { padding: 24px; }
.flex { display: flex; }
.flex-col { flex-direction: column; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.gap-1 { gap: 8px; }
.gap-2 { gap: 16px; }
.gap-3 { gap: 24px; }
.rounded { border-radius: 8px; }
.rounded-lg { border-radius: 12px; }
.shadow { box-shadow: 0 2px 8px rgba(0,0,0,0.1); }
