// 好友管理模块
class FriendsManager {
    constructor() {
        this.storageKey = 'mahjong_friends';
        this.init();
    }

    // 初始化
    init() {
        if (!this.getFriendsData()) {
            this.saveFriendsData({
                friends: {},
                lastUpdate: new Date().toISOString()
            });
        }
    }

    // 获取好友数据
    getFriendsData() {
        try {
            const data = localStorage.getItem(this.storageKey);
            return data ? JSON.parse(data) : null;
        } catch (error) {
            console.error('获取好友数据失败:', error);
            return null;
        }
    }

    // 保存好友数据
    saveFriendsData(data) {
        try {
            localStorage.setItem(this.storageKey, JSON.stringify(data));
            this.triggerFriendsUpdate();
        } catch (error) {
            console.error('保存好友数据失败:', error);
        }
    }

    // 从游戏数据中自动发现好友
    discoverFriendsFromGames() {
        const gameData = gameDataManager.getGameData();
        if (!gameData || !gameData.games) return;

        const friendsData = this.getFriendsData();
        const currentUser = this.getCurrentUserName();
        let hasNewFriends = false;

        Object.values(gameData.games).forEach(game => {
            game.players.forEach(player => {
                // 跳过当前用户自己
                if (player.name === currentUser || !player.name) return;

                const friendId = this.generateFriendId(player.name);
                
                if (!friendsData.friends[friendId]) {
                    // 发现新好友
                    friendsData.friends[friendId] = {
                        id: friendId,
                        name: player.name,
                        avatar: player.avatar || player.name.charAt(0),
                        addTime: new Date().toISOString(),
                        source: 'game',
                        gameHistory: [],
                        totalGames: 0,
                        winCount: 0,
                        loseCount: 0,
                        totalScore: 0,
                        lastPlayTime: null
                    };
                    hasNewFriends = true;
                }

                // 更新对战记录
                this.updateGameHistory(friendId, game, player, currentUser);
            });
        });

        if (hasNewFriends) {
            friendsData.lastUpdate = new Date().toISOString();
            this.saveFriendsData(friendsData);
        }
    }

    // 更新游戏历史记录
    updateGameHistory(friendId, game, player, currentUser) {
        const friendsData = this.getFriendsData();
        const friend = friendsData.friends[friendId];
        
        if (!friend) return;

        // 检查是否已经记录过这个游戏
        const existingGame = friend.gameHistory.find(g => g.gameId === game.id);
        if (existingGame) return;

        // 计算对战结果
        const currentPlayer = game.players.find(p => p.name === currentUser);
        const friendPlayer = player;

        const gameRecord = {
            gameId: game.id,
            gameName: game.name,
            gameDate: game.startTime,
            gameLocation: game.location,
            rounds: game.rounds.length,
            myScore: currentPlayer ? currentPlayer.totalScore : 0,
            friendScore: friendPlayer.totalScore,
            result: this.calculateGameResult(currentPlayer, friendPlayer),
            gameType: game.type
        };

        friend.gameHistory.push(gameRecord);
        friend.totalGames++;
        friend.totalScore += friendPlayer.totalScore;
        friend.lastPlayTime = game.startTime;

        // 更新胜负统计
        if (gameRecord.result === 'win') {
            friend.winCount++;
        } else if (gameRecord.result === 'lose') {
            friend.loseCount++;
        }

        this.saveFriendsData(friendsData);
    }

    // 计算游戏结果
    calculateGameResult(currentPlayer, friendPlayer) {
        if (!currentPlayer || !friendPlayer) return 'draw';
        
        if (currentPlayer.totalScore > friendPlayer.totalScore) {
            return 'win';
        } else if (currentPlayer.totalScore < friendPlayer.totalScore) {
            return 'lose';
        } else {
            return 'draw';
        }
    }

    // 获取当前用户名
    getCurrentUserName() {
        // 从用户资料或游戏数据中获取当前用户名
        const userProfile = JSON.parse(localStorage.getItem('userProfile') || '{}');
        if (userProfile.name) return userProfile.name;

        // 从游戏数据中获取（假设第一个玩家是当前用户）
        const gameData = gameDataManager.getGameData();
        if (gameData && gameData.games) {
            const games = Object.values(gameData.games);
            if (games.length > 0 && games[0].players.length > 0) {
                return games[0].players[0].name;
            }
        }

        return '张三'; // 默认用户名
    }

    // 生成好友ID
    generateFriendId(name) {
        return 'friend_' + name.replace(/\s+/g, '_') + '_' + Date.now().toString().slice(-6);
    }

    // 获取所有好友
    getAllFriends() {
        const friendsData = this.getFriendsData();
        if (!friendsData || !friendsData.friends) return [];

        return Object.values(friendsData.friends).sort((a, b) => {
            // 按最后游戏时间排序
            if (!a.lastPlayTime && !b.lastPlayTime) return 0;
            if (!a.lastPlayTime) return 1;
            if (!b.lastPlayTime) return -1;
            return new Date(b.lastPlayTime) - new Date(a.lastPlayTime);
        });
    }

    // 获取好友详情
    getFriendDetail(friendId) {
        const friendsData = this.getFriendsData();
        return friendsData?.friends[friendId] || null;
    }

    // 获取好友统计
    getFriendStats(friendId) {
        const friend = this.getFriendDetail(friendId);
        if (!friend) return null;

        const winRate = friend.totalGames > 0 ? 
            Math.round((friend.winCount / friend.totalGames) * 100) : 0;
        
        const avgScore = friend.totalGames > 0 ? 
            Math.round(friend.totalScore / friend.totalGames) : 0;

        return {
            totalGames: friend.totalGames,
            winCount: friend.winCount,
            loseCount: friend.loseCount,
            drawCount: friend.totalGames - friend.winCount - friend.loseCount,
            winRate: winRate,
            avgScore: avgScore,
            totalScore: friend.totalScore,
            lastPlayTime: friend.lastPlayTime
        };
    }

    // 搜索好友
    searchFriends(keyword) {
        const allFriends = this.getAllFriends();
        if (!keyword) return allFriends;

        return allFriends.filter(friend => 
            friend.name.toLowerCase().includes(keyword.toLowerCase())
        );
    }

    // 删除好友
    deleteFriend(friendId) {
        const friendsData = this.getFriendsData();
        if (friendsData && friendsData.friends[friendId]) {
            delete friendsData.friends[friendId];
            friendsData.lastUpdate = new Date().toISOString();
            this.saveFriendsData(friendsData);
            return true;
        }
        return false;
    }

    // 手动添加好友
    addFriend(name, avatar = null) {
        const friendsData = this.getFriendsData();
        const friendId = this.generateFriendId(name);

        // 检查是否已存在
        const existingFriend = Object.values(friendsData.friends).find(f => f.name === name);
        if (existingFriend) {
            return { success: false, message: '好友已存在' };
        }

        friendsData.friends[friendId] = {
            id: friendId,
            name: name,
            avatar: avatar || name.charAt(0),
            addTime: new Date().toISOString(),
            source: 'manual',
            gameHistory: [],
            totalGames: 0,
            winCount: 0,
            loseCount: 0,
            totalScore: 0,
            lastPlayTime: null
        };

        friendsData.lastUpdate = new Date().toISOString();
        this.saveFriendsData(friendsData);

        return { success: true, friendId: friendId };
    }

    // 获取好友数量
    getFriendsCount() {
        const friendsData = this.getFriendsData();
        return friendsData ? Object.keys(friendsData.friends).length : 0;
    }

    // 触发好友数据更新事件
    triggerFriendsUpdate() {
        const event = new CustomEvent('friendsDataUpdated', {
            detail: { timestamp: Date.now() }
        });
        window.dispatchEvent(event);
    }

    // 清除所有好友数据
    clearAllFriends() {
        this.saveFriendsData({
            friends: {},
            lastUpdate: new Date().toISOString()
        });
    }

    // 导入好友数据（保留用于数据恢复）
    importFriendsData(data) {
        if (data && typeof data === 'object' && data.friends) {
            this.saveFriendsData(data);
            return true;
        }
        return false;
    }
}

// 创建全局实例
const friendsManager = new FriendsManager();

// 导出到全局
window.friendsManager = friendsManager;

// 页面加载时自动发现好友
document.addEventListener('DOMContentLoaded', function() {
    // 延迟执行，确保gameDataManager已初始化
    setTimeout(() => {
        if (typeof gameDataManager !== 'undefined') {
            friendsManager.discoverFriendsFromGames();
        }
    }, 1000);
});

// 监听游戏数据更新，自动发现新好友
window.addEventListener('gameDataUpdated', function() {
    friendsManager.discoverFriendsFromGames();
});
