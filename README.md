# 麻将码 (MahjongCode) - iOS原型界面

## 项目概述

这是一个专为中国麻将爱好者设计的移动应用原型，通过二维码技术实现麻将牌局的快速记录、分享和统计分析。本项目包含完整的高保真iOS界面原型，严格遵循iOS设计规范。

## 功能特色

### 🎯 核心功能
- **二维码生成与扫描** - 快速创建和加入牌局
- **牌局记录管理** - 详细记录每局麻将数据
- **战绩统计分析** - 多维度数据分析和可视化
- **社交分享功能** - 与朋友分享精彩时刻

### 📱 界面特点
- **完全符合iOS设计规范** - 包含statusbar、navbar、homeindicator、safeArea
- **iPhone 15适配** - 393x852px屏幕尺寸优化
- **麻将主题设计** - 丰富的麻将元素和中国风配色
- **响应式布局** - 适配不同屏幕尺寸

## 文件结构

```
麻将码原型/
├── 麻将二维码app需求文档.md    # 详细需求文档
├── README.md                   # 项目说明
├── styles/
│   └── ios-base.css           # iOS基础样式文件
├── js/
│   ├── modal.js               # 自定义弹窗组件
│   └── gameData.js            # 游戏数据管理
├── index.html                 # 首页 - 主要入口
├── scan.html                  # 扫码页面 - 二维码扫描
├── create-game.html           # 创建牌局页面
├── game-detail.html           # 牌局详情页面
├── add-round.html             # 添加局次记录页面
├── stats.html                 # 战绩统计页面
├── profile.html               # 个人资料页面
├── history.html               # 历史记录页面
├── scan-history.html          # 扫描历史页面
├── qr-share.html              # 二维码分享页面
└── test-data.html             # 数据测试页面
```

## 页面说明

### 1. 首页 (index.html)
- **功能**: 应用主入口，展示快捷操作和最近牌局
- **特色**: 麻将主题背景动画，四宫格快捷操作
- **导航**: 扫码、创建牌局、统计、历史记录

### 2. 扫码页面 (scan.html)
- **功能**: 扫描二维码加入牌局
- **特色**: 仿真相机界面，扫描框动画效果
- **交互**: 手电筒开关，相册选择，手动输入

### 3. 创建牌局页面 (create-game.html)
- **功能**: 创建新的麻将牌局
- **特色**: 完整的表单设计，实时二维码预览
- **配置**: 玩家管理，规则设置，地点选择

### 4. 牌局详情页面 (game-detail.html)
- **功能**: 显示当前牌局状态和历史记录
- **特色**: 实时积分显示，局次记录列表
- **操作**: 添加局次，分享，结束游戏

### 5. 添加局次页面 (add-round.html)
- **功能**: 记录单局麻将结果
- **特色**: 直观的胜者选择，智能分数计算
- **验证**: 分数总和校验，必填项检查

### 6. 战绩统计页面 (stats.html)
- **功能**: 多维度数据分析展示
- **特色**: 图表可视化，时间筛选
- **数据**: 胜率、积分、游戏时长等

### 7. 个人资料页面 (profile.html)
- **功能**: 用户信息和应用设置
- **特色**: 个人统计概览，功能菜单
- **设置**: 深色模式，数据备份，帮助中心

### 8. 历史记录页面 (history.html)
- **功能**: 浏览所有历史牌局
- **特色**: 时间分组，多维筛选
- **搜索**: 关键词搜索，高级筛选

## 设计规范

### 🎨 色彩方案
- **主色调**: 深绿色 (#2E7D32) - 麻将桌色
- **辅助色**: 亮绿色 (#4CAF50)
- **强调色**: 橙红色 (#FF6B35) - 红中色
- **背景色**: 浅灰色 (#F5F5F5)

### 📐 尺寸规范
- **设备尺寸**: 393x852px (iPhone 15)
- **状态栏**: 54px高度
- **导航栏**: 44px高度
- **Home指示器**: 34px高度
- **安全区域**: 自动适配

### 🔤 字体规范
- **标题**: 20-28px, 700字重
- **正文**: 16px, 400字重
- **说明**: 14px, 400字重
- **标签**: 12px, 500字重

## 最新更新

### 🎉 **v1.1.0 新功能**
- **自定义弹窗系统** - 替换所有系统弹窗为应用内美观弹窗
- **本地数据管理** - 完整的游戏数据存储和管理系统
- **实时数据更新** - 保存局次后立即更新界面显示
- **数据持久化** - 使用localStorage保存游戏数据
- **Toast消息提示** - 轻量级操作反馈系统

### 🔧 **数据管理功能**
- **游戏状态管理** - 自动保存和恢复游戏进度
- **局次记录** - 完整的局次数据存储和统计
- **玩家积分** - 实时计算和显示玩家总分
- **数据导入导出** - 支持数据备份和恢复
- **多游戏支持** - 可以管理多个并行游戏

### 🎨 **弹窗系统特色**
- **iOS风格设计** - 圆角、阴影、模糊效果
- **多种弹窗类型** - 确认、提示、输入、选择对话框
- **动画效果** - 流畅的淡入淡出和缩放动画
- **键盘支持** - ESC关闭、自动聚焦等
- **Toast通知** - 成功、错误、警告等类型提示

## 技术特点

### 📱 iOS原生体验
- 完整的iOS界面元素 (状态栏、导航栏、Home指示器)
- 符合Human Interface Guidelines
- 原生手势和交互动画
- 深色模式支持

### 🎭 麻将主题
- 丰富的麻将元素装饰
- 中国传统色彩搭配
- 麻将牌图标和背景
- 文化相关的用户体验

### 🔧 技术实现
- 纯HTML5 + CSS3 + JavaScript
- 响应式设计
- FontAwesome图标库
- CSS动画和过渡效果

## 使用说明

### 🚀 快速开始
1. 直接在浏览器中打开 `index.html`
2. 推荐使用Chrome或Safari浏览器
3. 可以使用开发者工具模拟移动设备

### 📱 最佳体验
- 在移动设备上访问获得最佳体验
- 支持触摸手势和滑动操作
- 建议添加到主屏幕使用

### 🔗 页面导航
- 所有页面都有完整的导航链接
- 支持前进后退操作
- 底部导航栏快速切换

## 开发说明

### 🛠 本地开发
```bash
# 启动本地服务器 (可选)
python -m http.server 8000
# 或使用Node.js
npx serve .
```

### 📝 自定义修改
- 修改 `styles/ios-base.css` 调整全局样式
- 各页面有独立的样式定义
- JavaScript交互逻辑可根据需要扩展

### 🔄 数据模拟
- 当前使用静态数据展示
- 可以替换为真实API调用
- 本地存储可用于数据持久化

## 后续开发

### 📋 待实现功能
- [ ] 真实的二维码生成和识别
- [ ] 数据持久化存储
- [ ] 用户认证系统
- [ ] 社交分享集成
- [ ] 推送通知
- [ ] 离线模式支持

### 🚀 技术升级
- [ ] 转换为React Native应用
- [ ] 集成真实后端API
- [ ] 添加单元测试
- [ ] 性能优化
- [ ] 国际化支持

## 联系信息

如有任何问题或建议，请联系开发团队。

---

**麻将码 v1.0.0** - 让麻将记录更简单，让数据分析更智能！
