<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>麻将码 - 牌局详情</title>
    <link rel="stylesheet" href="styles/ios-base.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="js/modal.js"></script>
    <script src="js/gameData.js"></script>
    <style>
        .game-header {
            background: linear-gradient(135deg, #2E7D32 0%, #4CAF50 100%);
            color: white;
            padding: 24px 16px;
            position: relative;
            overflow: hidden;
        }
        
        .game-header::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 200%;
            height: 200%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><text y="50" font-size="25" fill="rgba(255,255,255,0.1)">🀄</text></svg>') repeat;
            background-size: 60px 60px;
            animation: float 25s linear infinite;
        }
        
        .game-info {
            position: relative;
            z-index: 2;
        }
        
        .game-title {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 8px;
        }
        
        .game-meta {
            display: flex;
            align-items: center;
            gap: 16px;
            font-size: 14px;
            opacity: 0.9;
        }
        
        .game-status {
            display: inline-flex;
            align-items: center;
            gap: 4px;
            background: rgba(255,255,255,0.2);
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
        }
        
        .players-section {
            padding: 24px 16px;
        }
        
        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 16px;
        }
        
        .players-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            margin-bottom: 24px;
        }
        
        .player-card {
            background: white;
            border-radius: 12px;
            padding: 16px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            position: relative;
        }
        
        .player-card.winner {
            border: 2px solid var(--accent-color);
            background: linear-gradient(135deg, #FFF3E0 0%, #FFECB3 100%);
        }
        
        .player-card.winner::before {
            content: '👑';
            position: absolute;
            top: -8px;
            right: -8px;
            background: var(--accent-color);
            width: 24px;
            height: 24px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
        }
        
        .player-avatar {
            width: 60px;
            height: 60px;
            border-radius: 30px;
            background: var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            font-weight: 600;
            margin: 0 auto 12px;
        }
        
        .player-name {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 4px;
        }
        
        .player-score {
            font-size: 20px;
            font-weight: 700;
            color: var(--primary-color);
        }
        
        .player-score.negative {
            color: var(--ios-red);
        }
        
        .rounds-section {
            padding: 0 16px 24px;
        }
        
        .rounds-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        
        .add-round-btn {
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 20px;
            padding: 8px 16px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 4px;
        }
        
        .round-item {
            background: white;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .round-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }
        
        .round-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
        }
        
        .round-time {
            font-size: 12px;
            color: var(--text-secondary);
        }
        
        .round-scores {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 8px;
        }
        
        .score-item {
            text-align: center;
            padding: 8px;
            background: var(--background-color);
            border-radius: 8px;
        }
        
        .score-name {
            font-size: 12px;
            color: var(--text-secondary);
            margin-bottom: 4px;
        }
        
        .score-value {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
        }
        
        .score-value.winner {
            color: var(--accent-color);
        }
        
        .score-value.negative {
            color: var(--ios-red);
        }
        
        .game-actions {
            padding: 16px;
            background: white;
            border-top: 1px solid var(--border-color);
            display: flex;
            gap: 20px;
            justify-content: center;
        }
        
        .action-btn {
            min-width: 120px;
            padding: 16px 24px;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
            border: none;
        }

        .action-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
        }

        .action-btn:disabled:hover {
            transform: none !important;
        }
        
        .btn-end {
            background: var(--ios-red);
            color: white;
        }

        .btn-qr {
            background: var(--primary-color);
            color: white;
        }
        
        .floating-add {
            position: fixed;
            bottom: 100px;
            right: 20px;
            width: 56px;
            height: 56px;
            background: var(--primary-color);
            border-radius: 28px;
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(46, 125, 50, 0.3);
            transition: all 0.2s;
            z-index: 10;
        }
        
        .floating-add:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 16px rgba(46, 125, 50, 0.4);
        }
    </style>
</head>
<body>
    <div class="device-container">
        <div class="screen">
            <!-- 状态栏 -->
            <div class="status-bar">
                <div class="status-left">
                    <span>9:41</span>
                </div>
                <div class="status-right">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <div class="battery">
                        <div class="battery-fill"></div>
                    </div>
                </div>
            </div>
            
            <!-- 导航栏 -->
            <div class="nav-bar">
                <a href="index.html" class="nav-button">
                    <i class="fas fa-arrow-left"></i>
                </a>
                <div class="nav-title">牌局详情</div>
            </div>
            
            <!-- 安全区域内容 -->
            <div class="safe-area">
                <!-- 牌局信息 -->
                <div class="game-header">
                    <div class="game-info">
                        <div class="game-title">周末家庭局</div>
                        <div class="game-meta">
                            <span><i class="fas fa-calendar"></i> 今天 14:30</span>
                            <span><i class="fas fa-map-marker-alt"></i> 家里客厅</span>
                            <div class="game-status">
                                <i class="fas fa-play"></i>
                                进行中
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 玩家信息 -->
                <div class="players-section">
                    <h2 class="section-title">当前积分</h2>
                    <div class="players-grid" id="playersGrid">
                        <!-- 玩家卡片将通过JavaScript动态生成 -->
                    </div>
                </div>
                
                <!-- 局次记录 -->
                <div class="rounds-section">
                    <div class="rounds-header">
                        <h2 class="section-title" id="roundsTitle">局次记录 (0局)</h2>
                        <button class="add-round-btn" onclick="addRound()">
                            <i class="fas fa-plus"></i>
                            记录
                        </button>
                    </div>

                    <div id="roundsList">
                        <!-- 局次记录将通过JavaScript动态生成 -->
                    </div>

                    <div id="noRoundsMessage" class="text-center" style="padding: 40px 20px; color: var(--text-secondary); display: none;">
                        <i class="fas fa-gamepad" style="font-size: 48px; opacity: 0.3; margin-bottom: 16px;"></i>
                        <div style="font-size: 16px; margin-bottom: 8px;">还没有局次记录</div>
                        <div style="font-size: 14px;">点击上方"记录"按钮开始记录第一局</div>
                    </div>
                </div>
                
                <!-- 操作按钮 -->
                <div class="game-actions">
                    <button class="action-btn btn-qr" onclick="showQR()">
                        <i class="fas fa-qrcode"></i> 二维码
                    </button>
                    <button class="action-btn btn-end" onclick="endGame()">
                        <i class="fas fa-stop"></i> 结束
                    </button>
                </div>
            </div>
            
            <!-- 悬浮添加按钮 -->
            <button class="floating-add" onclick="addRound()">
                <i class="fas fa-plus"></i>
            </button>
            
            <!-- Home指示器 -->
            <div class="home-indicator">
                <div class="home-indicator-bar"></div>
            </div>
        </div>
    </div>
    
    <script>
        // 页面加载时初始化数据
        document.addEventListener('DOMContentLoaded', function() {
            // 检查是否有更新参数
            const urlParams = new URLSearchParams(window.location.search);
            const updated = urlParams.get('updated');

            if (updated) {
                // 如果有更新参数，清除URL参数并强制刷新数据
                window.history.replaceState({}, document.title, window.location.pathname);
                console.log('Data was updated, force reloading...');
            }

            loadGameData();
        });

        // 页面显示时重新加载数据（包括从其他页面返回）
        document.addEventListener('visibilitychange', function() {
            if (!document.hidden) {
                loadGameData();
            }
        });

        // 页面获得焦点时重新加载数据
        window.addEventListener('focus', function() {
            loadGameData();
        });

        // 监听存储变化（当其他页面修改了数据）
        window.addEventListener('storage', function(e) {
            if (e.key === 'mahjongGameData') {
                loadGameData();
            }
        });

        // 监听游戏数据更新事件
        window.addEventListener('gameDataUpdated', function(e) {
            console.log('Game data updated, reloading...', e.detail);
            loadGameData();
        });

        // 定期检查数据更新（作为备用方案）
        let lastDataCheck = Date.now();
        let lastRoundsCount = 0;

        setInterval(() => {
            const currentGame = gameDataManager.getCurrentGame();
            if (currentGame && currentGame.rounds.length !== lastRoundsCount) {
                console.log('Detected rounds count change:', lastRoundsCount, '->', currentGame.rounds.length);
                lastRoundsCount = currentGame.rounds.length;
                loadGameData();
            }
        }, 1000); // 每秒检查一次

        // 加载游戏数据
        function loadGameData() {
            console.log('Loading game data...');
            const currentGame = gameDataManager.getCurrentGame();
            if (!currentGame) {
                console.error('No current game found');
                return;
            }

            console.log('Current game:', currentGame);
            console.log('Game status:', currentGame.status);
            console.log('Rounds count:', currentGame.rounds.length);

            // 更新页面标题和信息
            updateGameInfo(currentGame);

            // 更新玩家积分
            updatePlayersDisplay(currentGame);

            // 更新局次记录
            updateRoundsDisplay(currentGame);

            // 根据游戏状态控制UI
            updateUIForGameStatus(currentGame);

            // 更新局次计数
            lastRoundsCount = currentGame.rounds.length;

            console.log('Game data loaded successfully');
        }

        // 更新游戏信息
        function updateGameInfo(game) {
            // 这里可以更新游戏标题、时间等信息
            document.title = `麻将码 - ${game.name}`;
        }

        // 根据游戏状态更新UI
        function updateUIForGameStatus(game) {
            const isCompleted = game.status === 'completed';

            // 控制悬浮添加按钮
            const floatingAddBtn = document.querySelector('.floating-add');
            if (floatingAddBtn) {
                if (isCompleted) {
                    floatingAddBtn.style.display = 'none';
                } else {
                    floatingAddBtn.style.display = 'block';
                }
            }

            // 控制操作按钮
            const endGameBtn = document.querySelector('.btn-end');
            if (endGameBtn) {
                if (isCompleted) {
                    endGameBtn.textContent = '已结束';
                    endGameBtn.style.background = 'var(--text-secondary)';
                    endGameBtn.style.cursor = 'not-allowed';
                    endGameBtn.disabled = true;
                } else {
                    endGameBtn.innerHTML = '<i class="fas fa-stop"></i> 结束';
                    endGameBtn.style.background = 'var(--ios-red)';
                    endGameBtn.style.cursor = 'pointer';
                    endGameBtn.disabled = false;
                }
            }

            // 更新页面标题显示游戏状态
            const navTitle = document.querySelector('.nav-title');
            if (navTitle) {
                if (isCompleted) {
                    navTitle.textContent = `牌局详情 (已结束)`;
                } else {
                    navTitle.textContent = '牌局详情';
                }
            }

            console.log(`UI已更新为${isCompleted ? '已结束' : '进行中'}状态`);
        }

        // 更新玩家显示
        function updatePlayersDisplay(game) {
            const playersGrid = document.getElementById('playersGrid');
            if (!playersGrid) return;

            // 找出当前领先的玩家
            const leadingPlayer = game.players.reduce((prev, current) =>
                (current.totalScore > prev.totalScore) ? current : prev
            );

            playersGrid.innerHTML = game.players.map(player => {
                const isWinner = player.totalScore === leadingPlayer.totalScore && player.totalScore > 0;
                const scoreClass = player.totalScore > 0 ? '' : 'negative';
                const cardClass = isWinner ? 'player-card winner' : 'player-card';
                const scoreText = player.totalScore > 0 ? `+${player.totalScore}` : player.totalScore.toString();

                return `
                    <div class="${cardClass}">
                        <div class="player-avatar">${player.avatar}</div>
                        <div class="player-name">${player.name}</div>
                        <div class="player-score ${scoreClass}">${scoreText}</div>
                    </div>
                `;
            }).join('');
        }

        // 更新局次记录显示
        function updateRoundsDisplay(game) {
            console.log('Updating rounds display, rounds:', game.rounds);
            const roundsList = document.getElementById('roundsList');
            const roundsTitle = document.getElementById('roundsTitle');
            const noRoundsMessage = document.getElementById('noRoundsMessage');

            if (!roundsList || !roundsTitle) {
                console.error('Required elements not found');
                return;
            }

            // 更新标题
            roundsTitle.textContent = `局次记录 (${game.rounds.length}局)`;
            console.log('Updated title to:', roundsTitle.textContent);

            if (game.rounds.length === 0) {
                console.log('No rounds found, showing empty message');
                roundsList.innerHTML = '';
                if (noRoundsMessage) {
                    noRoundsMessage.style.display = 'block';
                }
                return;
            }

            if (noRoundsMessage) {
                noRoundsMessage.style.display = 'none';
            }

            // 按时间倒序显示局次（最新的在前面）
            const sortedRounds = [...game.rounds].reverse();

            roundsList.innerHTML = sortedRounds.map(round => {
                const roundTime = new Date(round.timestamp).toLocaleTimeString('zh-CN', {
                    hour: '2-digit',
                    minute: '2-digit'
                });

                const scoresHtml = game.players.map(player => {
                    // 直接使用玩家姓名作为键名，与保存时保持一致
                    const score = round.scores[player.name] || 0;
                    const isWinner = round.winner === player.name;
                    const scoreClass = isWinner ? 'winner' : (score < 0 ? 'negative' : '');
                    const scoreText = score > 0 ? `+${score}` : score.toString();

                    return `
                        <div class="score-item">
                            <div class="score-name">${player.name}</div>
                            <div class="score-value ${scoreClass}">${scoreText}</div>
                        </div>
                    `;
                }).join('');

                return `
                    <div class="round-item">
                        <div class="round-header">
                            <div class="round-title">第${round.roundNumber}局</div>
                            <div class="round-time">${roundTime}</div>
                        </div>
                        <div class="round-scores">
                            ${scoresHtml}
                        </div>
                        ${round.notes ? `<div style="margin-top: 8px; font-size: 12px; color: var(--text-secondary); padding: 8px; background: var(--background-color); border-radius: 6px;">${round.notes}</div>` : ''}
                    </div>
                `;
            }).join('');
        }



        function addRound() {
            const currentGame = gameDataManager.getCurrentGame();
            if (!currentGame) {
                showToast('没有找到当前游戏', 'error');
                return;
            }

            if (currentGame.status === 'completed') {
                showToast('游戏已结束，无法添加新局次', 'error');
                return;
            }

            window.location.href = 'add-round.html';
        }

        function showQR() {
            window.location.href = 'qr-share.html';
        }



        async function endGame() {
            const result = await showConfirm(
                '结束牌局',
                '确认结束当前牌局？\n结束后将生成战绩报告。',
                {
                    confirmText: '结束牌局',
                    cancelText: '继续游戏',
                    confirmClass: 'danger'
                }
            );

            if (result) {
                console.log('=== 开始结束游戏流程 ===');
                console.log('gameDataManager是否存在:', typeof gameDataManager !== 'undefined');

                // 获取当前游戏信息
                const currentGame = gameDataManager.getCurrentGame();
                console.log('当前游戏信息:', currentGame);

                if (!currentGame) {
                    console.error('没有找到当前游戏');
                    showToast('没有找到当前游戏，无法结束', 'error');
                    return;
                }

                if (currentGame.status === 'completed') {
                    console.log('游戏已经结束，直接跳转');
                    showToast('牌局已结束', 'success');
                    setTimeout(() => {
                        window.location.replace('index.html');
                    }, 1000);
                    return;
                }

                console.log('结束前游戏状态:', currentGame.status);

                try {
                    // 调用gameDataManager结束游戏
                    console.log('调用endGame函数...');
                    const success = await gameDataManager.endGame();
                    console.log('endGame调用结果:', success);

                    if (success) {
                        // 验证状态是否更新
                        const updatedGame = gameDataManager.getCurrentGame();
                        console.log('结束后游戏状态:', updatedGame.status);
                        console.log('结束时间:', updatedGame.endTime);

                        // 验证localStorage中的数据
                        const storageData = localStorage.getItem('mahjongGameData');
                        if (storageData) {
                            const parsedData = JSON.parse(storageData);
                            const savedGame = parsedData.games[currentGame.id];
                            console.log('localStorage中保存的游戏状态:', savedGame?.status);
                            console.log('localStorage中保存的结束时间:', savedGame?.endTime);
                        }

                        showToast('牌局已结束，正在保存记录...', 'success');

                        // 确保数据完全保存后再触发事件
                        await new Promise(resolve => {
                            setTimeout(() => {
                                console.log('第一次触发数据更新事件');
                                gameDataManager.triggerDataUpdate();
                                resolve();
                            }, 50);
                        });

                        // 再次触发事件确保所有页面都能接收到
                        await new Promise(resolve => {
                            setTimeout(() => {
                                console.log('第二次触发数据更新事件');
                                gameDataManager.triggerDataUpdate();
                                resolve();
                            }, 200);
                        });

                        // 最后一次触发事件并等待足够时间
                        await new Promise(resolve => {
                            setTimeout(() => {
                                console.log('第三次触发数据更新事件');
                                gameDataManager.triggerDataUpdate();

                                // 手动触发storage事件确保跨页面通信
                                const storageEvent = new StorageEvent('storage', {
                                    key: 'mahjongGameData',
                                    newValue: localStorage.getItem('mahjongGameData'),
                                    url: window.location.href
                                });
                                window.dispatchEvent(storageEvent);

                                resolve();
                            }, 500);
                        });

                        console.log('准备跳转到首页');
                        showToast('记录已保存，即将返回首页', 'success');

                        // 最终跳转前再等待一段时间确保事件传播
                        setTimeout(() => {
                            console.log('执行页面跳转');
                            window.location.replace('index.html');
                        }, 1000);

                    } else {
                        console.error('结束游戏失败');
                        showToast('结束游戏失败，请重试', 'error');
                    }
                } catch (error) {
                    console.error('结束游戏过程中发生错误:', error);
                    showToast('结束游戏时发生错误，请重试', 'error');
                }
            }
        }
    </script>
</body>
</html>
