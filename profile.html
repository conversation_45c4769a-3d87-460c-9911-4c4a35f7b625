<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>麻将码 - 我的</title>
    <link rel="stylesheet" href="styles/ios-base.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="js/modal.js"></script>
    <script src="js/gameData.js"></script>
    <script src="js/avatarLibrary.js"></script>
    <style>
        .profile-header {
            background: linear-gradient(135deg, #2E7D32 0%, #4CAF50 100%);
            color: white;
            padding: 32px 16px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .profile-header::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 200%;
            height: 200%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><text y="50" font-size="30" fill="rgba(255,255,255,0.1)">👤</text></svg>') repeat;
            background-size: 80px 80px;
            animation: float 30s linear infinite;
        }
        
        .profile-content {
            position: relative;
            z-index: 2;
        }
        
        .avatar-container {
            position: relative;
            display: inline-block;
            margin-bottom: 16px;
        }
        
        .user-avatar {
            width: 80px;
            height: 80px;
            border-radius: 40px;
            background: rgba(255,255,255,0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 36px;
            backdrop-filter: blur(10px);
            border: 3px solid rgba(255,255,255,0.3);
            overflow: hidden;
            position: relative;
        }

        .user-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 40px;
        }

        .user-avatar .avatar-icon {
            font-size: 40px;
            color: white;
        }
        

        
        .user-name {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 4px;
            cursor: pointer;
            padding: 4px 8px;
            border-radius: 6px;
            transition: background-color 0.2s;
        }

        .user-name:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        .edit-hint {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.7);
            margin-top: 4px;
        }
        
        .user-id {
            font-size: 14px;
            opacity: 0.8;
            margin-bottom: 16px;
        }
        
        .user-stats {
            display: flex;
            justify-content: space-around;
            background: rgba(255,255,255,0.2);
            border-radius: 16px;
            padding: 16px;
            backdrop-filter: blur(10px);
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-value {
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 4px;
        }
        
        .stat-label {
            font-size: 12px;
            opacity: 0.9;
        }
        
        .menu-section {
            padding: 24px 16px;
        }
        
        .menu-group {
            background: white;
            border-radius: 16px;
            margin-bottom: 16px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .menu-item {
            display: flex;
            align-items: center;
            padding: 16px 20px;
            border-bottom: 1px solid var(--border-color);
            text-decoration: none;
            color: var(--text-primary);
            transition: background-color 0.2s;
        }
        
        .menu-item:last-child {
            border-bottom: none;
        }
        
        .menu-item:hover {
            background: var(--background-color);
        }
        
        .menu-icon {
            width: 32px;
            height: 32px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 16px;
            font-size: 16px;
            color: white;
        }
        
        .icon-history {
            background: var(--ios-blue);
        }
        
        .icon-friends {
            background: var(--ios-green);
        }
        
        .icon-settings {
            background: var(--ios-gray);
        }
        
        .icon-help {
            background: var(--ios-orange);
        }
        
        .icon-about {
            background: var(--ios-purple, #AF52DE);
        }
        
        .icon-backup {
            background: var(--primary-color);
        }
        
        .icon-share {
            background: var(--accent-color);
        }
        
        .menu-content {
            flex: 1;
        }
        
        .menu-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 2px;
        }
        
        .menu-subtitle {
            font-size: 12px;
            color: var(--text-secondary);
        }
        
        .menu-arrow {
            color: var(--ios-gray);
            font-size: 14px;
        }
        
        .menu-badge {
            background: var(--ios-red);
            color: white;
            font-size: 10px;
            font-weight: 600;
            padding: 2px 6px;
            border-radius: 8px;
            margin-right: 8px;
        }
        
        .version-info {
            text-align: center;
            padding: 24px 16px;
            color: var(--text-secondary);
            font-size: 12px;
        }
        
        .bottom-nav {
            background: white;
            border-top: 1px solid var(--border-color);
            padding: 8px 0;
            display: flex;
            justify-content: space-around;
            position: sticky;
            bottom: 0;
            box-shadow: 0 -2px 8px rgba(0,0,0,0.1);
            max-width: 400px;
            margin: 0 auto;
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 12px 16px;
            text-decoration: none;
            color: var(--ios-gray);
            transition: all 0.2s;
            border-radius: 12px;
            min-width: 60px;
        }

        .nav-item.active {
            color: var(--primary-color);
            background: rgba(46, 125, 50, 0.1);
        }

        .nav-item:hover {
            background: rgba(0, 0, 0, 0.05);
        }
        
        .nav-item i {
            font-size: 24px;
            margin-bottom: 4px;
        }
        
        .nav-item span {
            font-size: 10px;
            font-weight: 500;
        }
        
        .toggle-switch {
            width: 44px;
            height: 26px;
            background: var(--ios-gray);
            border-radius: 13px;
            position: relative;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        
        .toggle-switch.active {
            background: var(--primary-color);
        }
        
        .toggle-switch::after {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 22px;
            height: 22px;
            background: white;
            border-radius: 11px;
            transition: transform 0.2s;
        }
        
        .toggle-switch.active::after {
            transform: translateX(18px);
        }
    </style>
</head>
<body>
    <div class="device-container">
        <div class="screen">
            <!-- 状态栏 -->
            <div class="status-bar">
                <div class="status-left">
                    <span>9:41</span>
                </div>
                <div class="status-right">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <div class="battery">
                        <div class="battery-fill"></div>
                    </div>
                </div>
            </div>
            
            <!-- 导航栏 -->
            <div class="nav-bar">
                <button class="nav-button">
                    <i class="fas fa-cog"></i>
                </button>
                <div class="nav-title">我的</div>
                <button class="nav-button">
                    <i class="fas fa-bell"></i>
                </button>
            </div>
            
            <!-- 安全区域内容 -->
            <div class="safe-area">
                <!-- 个人信息头部 -->
                <div class="profile-header">
                    <div class="profile-content">
                        <div class="avatar-container">
                            <div class="user-avatar" id="userAvatar">🀄</div>
                        </div>

                        <div class="user-name" onclick="editNickname()">张三</div>
                        <div class="edit-hint">点击修改昵称</div>
                        <div class="user-id">ID: MJ2024001</div>
                        
                        <div class="user-stats">
                            <div class="stat-item">
                                <div class="stat-value">28</div>
                                <div class="stat-label">总局数</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">64%</div>
                                <div class="stat-label">胜率</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">15</div>
                                <div class="stat-label">好友</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 菜单区域 -->
                <div class="menu-section">
                    <!-- 游戏相关 -->
                    <div class="menu-group">
                        <a href="history.html" class="menu-item">
                            <div class="menu-icon icon-history">
                                <i class="fas fa-history"></i>
                            </div>
                            <div class="menu-content">
                                <div class="menu-title">历史记录</div>
                                <div class="menu-subtitle">查看所有牌局记录</div>
                            </div>
                            <i class="fas fa-chevron-right menu-arrow"></i>
                        </a>
                    </div>
                    
                    <!-- 应用设置 -->
                    <div class="menu-group">
                        <div class="menu-item">
                            <div class="menu-icon icon-settings">
                                <i class="fas fa-moon"></i>
                            </div>
                            <div class="menu-content">
                                <div class="menu-title">深色模式</div>
                                <div class="menu-subtitle">护眼模式开关</div>
                            </div>
                            <div class="toggle-switch" onclick="toggleDarkMode(this)"></div>
                        </div>
                    </div>
                </div>
                
                <!-- 版本信息 -->
                <div class="version-info">
                    麻将码 v1.0.0<br>
                    © 2024 MahjongCode. All rights reserved.
                </div>
                
                <!-- 底部导航 -->
                <div class="bottom-nav">
                    <a href="index.html" class="nav-item">
                        <i class="fas fa-home"></i>
                        <span>首页</span>
                    </a>
                    <a href="stats.html" class="nav-item">
                        <i class="fas fa-chart-line"></i>
                        <span>统计</span>
                    </a>
                    <a href="profile.html" class="nav-item active">
                        <i class="fas fa-user"></i>
                        <span>我的</span>
                    </a>
                </div>
            </div>
            
            <!-- Home指示器 -->
            <div class="home-indicator">
                <div class="home-indicator-bar"></div>
            </div>
        </div>
    </div>
    
    <script>
        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadUserProfile();
            loadUserStats();
        });

        // 加载用户资料
        function loadUserProfile() {
            // 这里可以从本地存储或服务器加载用户信息
            const userInfo = getUserInfo();
            updateProfileDisplay(userInfo);
        }

        // 获取用户信息
        function getUserInfo() {
            const saved = localStorage.getItem('userProfile');
            if (saved) {
                const userInfo = JSON.parse(saved);
                // 确保头像始终是麻将图标
                userInfo.avatar = '🀄';
                return userInfo;
            }

            // 默认用户信息
            return {
                name: '麻将玩家',
                avatar: '🀄',
                phone: '138****8888',
                joinDate: new Date().toISOString().split('T')[0],
                level: '麻将新手',
                motto: '胡牌靠运气，技巧靠练习'
            };
        }

        // 更新资料显示
        function updateProfileDisplay(userInfo) {
            const nameElement = document.querySelector('.user-name');
            const avatarElement = document.getElementById('userAvatar');

            if (nameElement) nameElement.textContent = userInfo.name;

            // 头像始终显示为麻将图标
            if (avatarElement) {
                avatarElement.textContent = '🀄';
            }
        }

        // 加载用户统计
        function loadUserStats() {
            const gameData = gameDataManager.getGameData();
            if (!gameData || !gameData.games) {
                return;
            }

            const stats = calculateUserStats(gameData);
            updateStatsDisplay(stats);
        }

        // 计算用户统计
        function calculateUserStats(gameData) {
            const games = Object.values(gameData.games);
            let totalGames = games.length;
            let totalRounds = 0;
            let totalWins = 0;

            games.forEach(game => {
                totalRounds += game.rounds.length;
                const currentPlayer = game.players[0]; // 假设当前用户是第一个玩家
                if (currentPlayer) {
                    game.rounds.forEach(round => {
                        if (round.winner === currentPlayer.name) {
                            totalWins++;
                        }
                    });
                }
            });

            return {
                totalGames,
                totalRounds,
                totalWins,
                winRate: totalRounds > 0 ? Math.round((totalWins / totalRounds) * 100) : 0
            };
        }

        // 更新统计显示
        function updateStatsDisplay(stats) {
            const statsItems = document.querySelectorAll('.stats-item .stats-value');
            if (statsItems.length >= 3) {
                statsItems[0].textContent = stats.totalGames;
                statsItems[1].textContent = stats.totalRounds;
                statsItems[2].textContent = `${stats.winRate}%`;
            }
        }






        // 编辑昵称
        async function editNickname() {
            const currentName = document.querySelector('.user-name').textContent;
            const newName = await showPrompt(
                '修改昵称',
                '请输入新的昵称：',
                {
                    defaultValue: currentName,
                    placeholder: '输入昵称',
                    confirmText: '保存',
                    cancelText: '取消'
                }
            );

            if (newName && newName.trim() && newName.trim() !== currentName) {
                const trimmedName = newName.trim();

                // 更新显示
                document.querySelector('.user-name').textContent = trimmedName;

                // 保存到本地存储
                const userInfo = getUserInfo();
                userInfo.name = trimmedName;
                // 头像保持固定为麻将图标，不随昵称改变
                userInfo.avatar = '🀄';

                localStorage.setItem('userProfile', JSON.stringify(userInfo));

                showToast('昵称修改成功', 'success');
            }
        }

        // 深色模式切换
        function toggleDarkMode(element) {
            element.classList.toggle('active');
            const isEnabled = element.classList.contains('active');

            // 保存设置
            localStorage.setItem('darkMode', isEnabled);

            // 应用深色模式
            if (isEnabled) {
                document.body.classList.add('dark-mode');
            } else {
                document.body.classList.remove('dark-mode');
            }

            showToast(isEnabled ? '深色模式已开启' : '深色模式已关闭', 'success');
        }



        // 初始化深色模式
        function initDarkMode() {
            const isDark = localStorage.getItem('darkMode') === 'true';
            const toggle = document.querySelector('.toggle-switch');

            if (isDark) {
                document.body.classList.add('dark-mode');
                if (toggle) toggle.classList.add('active');
            }
        }









        // 页面加载完成后初始化深色模式
        document.addEventListener('DOMContentLoaded', function() {
            initDarkMode();
        });
    </script>
</body>
</html>
