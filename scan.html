<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>麻将码 - 扫码</title>
    <link rel="stylesheet" href="styles/ios-base.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="js/modal.js"></script>
    <style>
        .scan-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: #000;
            position: relative;
        }

        .camera-view {
            flex: 1;
            position: relative;
            background: linear-gradient(45deg, #1a1a1a 25%, transparent 25%),
                        linear-gradient(-45deg, #1a1a1a 25%, transparent 25%),
                        linear-gradient(45deg, transparent 75%, #1a1a1a 75%),
                        linear-gradient(-45deg, transparent 75%, #1a1a1a 75%);
            background-size: 20px 20px;
            background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        .camera-preview {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('https://images.unsplash.com/photo-1606041008023-472dfb5e530f?w=400&h=600&fit=crop') center/cover;
            opacity: 0.3;
            animation: cameraShake 0.1s infinite alternate;
        }

        @keyframes cameraShake {
            0% { transform: translateX(0px) translateY(0px); }
            100% { transform: translateX(1px) translateY(1px); }
        }
        
        .scan-frame {
            width: 250px;
            height: 250px;
            position: relative;
            border: 2px solid rgba(255,255,255,0.3);
        }
        
        .scan-corners {
            position: absolute;
            width: 100%;
            height: 100%;
        }
        
        .corner {
            position: absolute;
            width: 30px;
            height: 30px;
            border: 3px solid #4CAF50;
        }
        
        .corner.top-left {
            top: -3px;
            left: -3px;
            border-right: none;
            border-bottom: none;
        }
        
        .corner.top-right {
            top: -3px;
            right: -3px;
            border-left: none;
            border-bottom: none;
        }
        
        .corner.bottom-left {
            bottom: -3px;
            left: -3px;
            border-right: none;
            border-top: none;
        }
        
        .corner.bottom-right {
            bottom: -3px;
            right: -3px;
            border-left: none;
            border-top: none;
        }
        
        .scan-line {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 3px;
            background: linear-gradient(90deg, transparent, #4CAF50, #00E676, #4CAF50, transparent);
            animation: scan 2.5s ease-in-out infinite;
            box-shadow: 0 0 10px #4CAF50;
        }

        @keyframes scan {
            0% {
                top: 0;
                opacity: 1;
                transform: scaleX(0.8);
            }
            50% {
                opacity: 1;
                transform: scaleX(1);
            }
            100% {
                top: 100%;
                opacity: 0;
                transform: scaleX(0.8);
            }
        }

        .scan-particles {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }

        .particle {
            position: absolute;
            width: 2px;
            height: 2px;
            background: #4CAF50;
            border-radius: 50%;
            animation: particle 3s linear infinite;
        }

        @keyframes particle {
            0% {
                opacity: 0;
                transform: scale(0);
            }
            10% {
                opacity: 1;
                transform: scale(1);
            }
            90% {
                opacity: 1;
                transform: scale(1);
            }
            100% {
                opacity: 0;
                transform: scale(0);
            }
        }
        
        .scan-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                linear-gradient(to bottom, rgba(0,0,0,0.6) 0%, transparent 30%, transparent 70%, rgba(0,0,0,0.6) 100%),
                linear-gradient(to right, rgba(0,0,0,0.6) 0%, transparent 30%, transparent 70%, rgba(0,0,0,0.6) 100%);
        }
        
        .scan-instructions {
            position: absolute;
            bottom: 180px;
            left: 0;
            right: 0;
            text-align: center;
            color: white;
            padding: 0 32px;
            z-index: 5;
        }

        .scan-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .scan-subtitle {
            font-size: 14px;
            opacity: 0.8;
            line-height: 1.4;
        }
        
        .scan-controls {
            background: rgba(0,0,0,0.9);
            padding: 20px 24px;
            display: flex;
            justify-content: space-around;
            align-items: center;
            border-top: 1px solid rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
        }
        
        .control-button {
            width: 60px;
            height: 60px;
            border-radius: 30px;
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .control-button:hover {
            background: rgba(255,255,255,0.3);
            transform: scale(1.05);
        }
        
        .control-button.primary {
            background: var(--primary-color);
        }
        
        .control-button.primary:hover {
            background: #1B5E20;
        }
        
        .bottom-nav {
            background: rgba(0,0,0,0.9);
            border-top: 1px solid rgba(255,255,255,0.1);
            padding: 8px 0;
            display: flex;
            justify-content: space-around;
        }
        
        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 8px;
            text-decoration: none;
            color: rgba(255,255,255,0.6);
            transition: color 0.2s;
        }
        
        .nav-item.active {
            color: var(--primary-color);
        }
        
        .nav-item i {
            font-size: 24px;
            margin-bottom: 4px;
        }
        
        .nav-item span {
            font-size: 10px;
            font-weight: 500;
        }
        
        .torch-button {
            position: absolute;
            top: 20px;
            right: 20px;
            width: 44px;
            height: 44px;
            border-radius: 22px;
            background: rgba(0,0,0,0.5);
            border: none;
            color: white;
            font-size: 20px;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .torch-button.active {
            background: var(--primary-color);
        }
        
        .recent-codes {
            position: absolute;
            bottom: 280px;
            left: 20px;
            right: 20px;
            z-index: 5;
        }

        .recent-title {
            color: white;
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 12px;
            text-align: center;
        }

        .recent-list {
            display: flex;
            gap: 12px;
            overflow-x: auto;
            padding: 0 0 8px;
        }
        
        .recent-item {
            min-width: 80px;
            height: 80px;
            background: rgba(255,255,255,0.1);
            border-radius: 12px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: white;
            text-decoration: none;
            transition: all 0.2s;
            backdrop-filter: blur(10px);
        }
        
        .recent-item:hover {
            background: rgba(255,255,255,0.2);
            transform: translateY(-2px);
        }
        
        .recent-item i {
            font-size: 24px;
            margin-bottom: 4px;
        }
        
        .recent-item span {
            font-size: 10px;
            text-align: center;
        }

        .scan-status {
            position: absolute;
            top: 140px;
            left: 20px;
            right: 20px;
            text-align: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
            background: rgba(0,0,0,0.8);
            padding: 12px 20px;
            border-radius: 20px;
            backdrop-filter: blur(10px);
            transform: translateY(-20px);
            opacity: 0;
            transition: all 0.3s ease;
            z-index: 10;
            border: 1px solid rgba(255,255,255,0.2);
        }

        .scan-status.show {
            transform: translateY(0);
            opacity: 1;
        }

        .scan-status.success {
            background: rgba(76, 175, 80, 0.9);
        }

        .scan-status.error {
            background: rgba(244, 67, 54, 0.9);
        }

        .qr-detected {
            position: absolute;
            border: 3px solid #4CAF50;
            background: rgba(76, 175, 80, 0.1);
            border-radius: 8px;
            animation: qrPulse 1s ease-in-out infinite;
        }

        @keyframes qrPulse {
            0%, 100% {
                transform: scale(1);
                box-shadow: 0 0 0 0 rgba(76, 175, 80, 0.7);
            }
            50% {
                transform: scale(1.05);
                box-shadow: 0 0 0 10px rgba(76, 175, 80, 0);
            }
        }

        .scan-tips {
            position: absolute;
            bottom: 200px;
            left: 20px;
            right: 20px;
            text-align: center;
            color: white;
            font-size: 13px;
            opacity: 0.9;
            z-index: 5;
            background: rgba(0,0,0,0.3);
            border-radius: 12px;
            padding: 16px;
            backdrop-filter: blur(5px);
        }

        .tip-item {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            margin-bottom: 8px;
        }

        .tip-item:last-child {
            margin-bottom: 0;
        }

        .tip-icon {
            width: 20px;
            height: 20px;
            background: rgba(255,255,255,0.2);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            flex-shrink: 0;
        }

        .scan-history-btn {
            position: absolute;
            top: 20px;
            left: 20px;
            width: 44px;
            height: 44px;
            border-radius: 22px;
            background: rgba(0,0,0,0.5);
            border: none;
            color: white;
            font-size: 18px;
            cursor: pointer;
            transition: all 0.2s;
            backdrop-filter: blur(10px);
        }

        .scan-history-btn:hover {
            background: rgba(0,0,0,0.7);
            transform: scale(1.05);
        }
    </style>
</head>
<body>
    <div class="device-container">
        <div class="screen">
            <!-- 状态栏 -->
            <div class="status-bar" style="color: white;">
                <div class="status-left">
                    <span>9:41</span>
                </div>
                <div class="status-right">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <div class="battery" style="border-color: white;">
                        <div class="battery-fill"></div>
                    </div>
                </div>
            </div>
            
            <!-- 导航栏 -->
            <div class="nav-bar" style="background: rgba(0,0,0,0.8); border-bottom: 1px solid rgba(255,255,255,0.1);">
                <a href="index.html" class="nav-button" style="color: white;">
                    <i class="fas fa-arrow-left"></i>
                </a>
                <div class="nav-title" style="color: white;">扫描二维码</div>
                <button class="nav-button" style="color: white;">
                    <i class="fas fa-image"></i>
                </button>
            </div>
            
            <!-- 扫码容器 -->
            <div class="scan-container">
                <!-- 相机视图 -->
                <div class="camera-view">
                    <!-- 相机预览背景 -->
                    <div class="camera-preview"></div>

                    <div class="scan-overlay"></div>

                    <!-- 扫描历史按钮 -->
                    <button class="scan-history-btn" onclick="showScanHistory()">
                        <i class="fas fa-history"></i>
                    </button>

                    <!-- 手电筒按钮 -->
                    <button class="torch-button" onclick="toggleTorch()">
                        <i class="fas fa-flashlight"></i>
                    </button>

                    <!-- 扫描状态提示 -->
                    <div class="scan-status" id="scanStatus">
                        正在扫描二维码...
                    </div>

                    <!-- 扫描框 -->
                    <div class="scan-frame" id="scanFrame">
                        <div class="scan-corners">
                            <div class="corner top-left"></div>
                            <div class="corner top-right"></div>
                            <div class="corner bottom-left"></div>
                            <div class="corner bottom-right"></div>
                        </div>
                        <div class="scan-line"></div>
                        <div class="scan-particles" id="scanParticles"></div>
                    </div>

                    <!-- 扫描提示 -->
                    <div class="scan-tips">
                        <div class="tip-item">
                            <div class="tip-icon">💡</div>
                            <span>保持手机稳定，对准二维码</span>
                        </div>
                        <div class="tip-item">
                            <div class="tip-icon">📱</div>
                            <span>确保光线充足，距离适中</span>
                        </div>
                    </div>
                    
                    <!-- 扫描提示 -->
                    <div class="scan-tips">
                        <div class="tip-item">
                            <div class="tip-icon">💡</div>
                            <span>保持手机稳定，对准二维码</span>
                        </div>
                        <div class="tip-item">
                            <div class="tip-icon">📱</div>
                            <span>确保光线充足，距离适中</span>
                        </div>
                    </div>
                </div>
                
                <!-- 控制按钮 -->
                <div class="scan-controls">
                    <div style="text-align: center;">
                        <button class="control-button" onclick="openGallery()">
                            <i class="fas fa-image"></i>
                        </button>
                        <div style="color: white; font-size: 12px; margin-top: 8px;">相册</div>
                    </div>

                    <div style="text-align: center;">
                        <button class="control-button primary" onclick="manualInput()">
                            <i class="fas fa-keyboard"></i>
                        </button>
                        <div style="color: white; font-size: 12px; margin-top: 8px;">手动输入</div>
                    </div>

                    <div style="text-align: center;">
                        <button class="control-button" onclick="showHistory()">
                            <i class="fas fa-history"></i>
                        </button>
                        <div style="color: white; font-size: 12px; margin-top: 8px;">历史</div>
                    </div>
                </div>
                
                <!-- 底部导航 -->
                <div class="bottom-nav">
                    <a href="index.html" class="nav-item">
                        <i class="fas fa-home"></i>
                        <span>首页</span>
                    </a>
                    <a href="scan.html" class="nav-item active">
                        <i class="fas fa-qrcode"></i>
                        <span>扫码</span>
                    </a>
                    <a href="stats.html" class="nav-item">
                        <i class="fas fa-chart-line"></i>
                        <span>统计</span>
                    </a>
                    <a href="profile.html" class="nav-item">
                        <i class="fas fa-user"></i>
                        <span>我的</span>
                    </a>
                </div>
            </div>
            
            <!-- Home指示器 -->
            <div class="home-indicator">
                <div class="home-indicator-bar"></div>
            </div>
        </div>
    </div>
    
    <script>
        let isScanning = true;
        let torchEnabled = false;
        let scanningInterval;
        let particleInterval;

        // 初始化扫描功能
        function initScanner() {
            showScanStatus('正在启动相机...', 'normal');

            setTimeout(() => {
                hideScanStatus();
                startScanning();
            }, 2000);
        }

        // 隐藏扫描状态
        function hideScanStatus() {
            const status = document.getElementById('scanStatus');
            status.classList.remove('show');
        }

        // 开始扫描
        function startScanning() {
            isScanning = true;
            createParticles();

            // 模拟扫描检测
            scanningInterval = setInterval(() => {
                if (isScanning) {
                    simulateQRDetection();
                }
            }, 2000);
        }

        // 停止扫描
        function stopScanning() {
            isScanning = false;
            if (scanningInterval) {
                clearInterval(scanningInterval);
            }
            if (particleInterval) {
                clearInterval(particleInterval);
            }
        }

        // 显示扫描状态
        function showScanStatus(message, type = 'normal') {
            const status = document.getElementById('scanStatus');
            status.textContent = message;
            status.className = 'scan-status show';

            if (type === 'success') {
                status.classList.add('success');
            } else if (type === 'error') {
                status.classList.add('error');
            }

            if (type !== 'normal') {
                setTimeout(() => {
                    status.classList.remove('show');
                }, 3000);
            }
        }

        // 模拟二维码检测
        function simulateQRDetection() {
            const random = Math.random();

            if (random > 0.85) {
                // 检测到二维码
                detectQRCode();
            } else if (random > 0.7) {
                // 检测到但不清晰
                showScanStatus('二维码不够清晰，请调整角度', 'error');
            }
        }

        // 检测到二维码
        async function detectQRCode() {
            stopScanning();

            // 在扫描框中显示检测效果
            const frame = document.getElementById('scanFrame');
            frame.classList.add('qr-detected');

            showScanStatus('检测到二维码！', 'success');

            // 震动效果（如果支持）
            if (navigator.vibrate) {
                navigator.vibrate(200);
            }

            setTimeout(async () => {
                // 模拟扫描到的二维码内容
                const mockQRCodes = [
                    'MJ20240118001',
                    'MJ20240118002',
                    'https://mahjongcode.app/join/MJ20240118003',
                    'invalid_code'
                ];

                const scannedCode = mockQRCodes[Math.floor(Math.random() * mockQRCodes.length)];

                // 保存扫描历史
                saveScanHistory(scannedCode);

                // 解析二维码内容
                const parseResult = parseQRCode(scannedCode);

                if (parseResult.valid) {
                    // 查找对应的游戏
                    const gameInfo = findGameByCode(parseResult.gameId);

                    const message = gameInfo ?
                        `牌局：${gameInfo.name}\n人数：${gameInfo.players.length}人\n状态：${gameInfo.status === 'ongoing' ? '进行中' : '等待中'}\n\n是否加入此牌局？` :
                        `检测到麻将码：${parseResult.gameId}\n\n是否加入此牌局？`;

                    const result = await showConfirm('检测到麻将局二维码', message, {
                        confirmText: '加入牌局',
                        cancelText: '继续扫描'
                    });

                    if (result) {
                        await joinGameByCode(parseResult.gameId, gameInfo);
                    } else {
                        restartScanning();
                    }
                } else {
                    await showAlert('扫描失败', '无效的二维码格式');
                    restartScanning();
                }

                frame.classList.remove('qr-detected');
            }, 1000);
        }

        // 创建扫描粒子效果
        function createParticles() {
            const container = document.getElementById('scanParticles');

            particleInterval = setInterval(() => {
                if (!isScanning) return;

                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.top = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 2 + 's';

                container.appendChild(particle);

                // 3秒后移除粒子
                setTimeout(() => {
                    if (container.contains(particle)) {
                        container.removeChild(particle);
                    }
                }, 3000);
            }, 500);
        }

        // 手电筒控制
        function toggleTorch() {
            const button = document.querySelector('.torch-button');
            torchEnabled = !torchEnabled;
            button.classList.toggle('active');

            if (torchEnabled) {
                showScanStatus('手电筒已开启', 'normal');
                // 这里可以添加实际的手电筒控制代码
            } else {
                showScanStatus('手电筒已关闭', 'normal');
            }
        }

        // 打开相册
        async function openGallery() {
            stopScanning();
            showScanStatus('正在打开相册...', 'normal');

            setTimeout(async () => {
                const hasQR = Math.random() > 0.5;
                if (hasQR) {
                    showScanStatus('从相册识别成功！', 'success');
                    setTimeout(async () => {
                        const result = await showConfirm(
                            '识别成功',
                            '从相册中识别到麻将局二维码\n\n牌局：朋友聚会\n是否加入此牌局？',
                            {
                                confirmText: '加入牌局',
                                cancelText: '继续扫描'
                            }
                        );

                        if (result) {
                            showToast('正在加入牌局...', 'success');
                            setTimeout(() => {
                                window.location.href = 'game-detail.html';
                            }, 1000);
                        } else {
                            hideScanStatus();
                            startScanning();
                        }
                    }, 1000);
                } else {
                    showScanStatus('未在图片中找到二维码', 'error');
                    showToast('未找到有效的二维码', 'error');
                    setTimeout(() => {
                        hideScanStatus();
                        startScanning();
                    }, 2000);
                }
            }, 1500);
        }

        // 手动输入
        async function manualInput() {
            stopScanning();

            const code = await showPrompt(
                '手动输入',
                '请输入二维码内容或牌局ID：',
                {
                    placeholder: '例如：MJ20240118001',
                    confirmText: '验证',
                    cancelText: '取消'
                }
            );

            if (code && code.trim()) {
                showScanStatus('正在验证...', 'normal');

                setTimeout(() => {
                    if (code.toLowerCase().includes('mj') || code.length >= 6) {
                        showScanStatus('验证成功！', 'success');
                        showToast('验证成功，正在加入牌局...', 'success');
                        setTimeout(() => {
                            window.location.href = 'game-detail.html';
                        }, 1000);
                    } else {
                        showScanStatus('无效的二维码内容', 'error');
                        showToast('请输入有效的牌局ID', 'error');
                        setTimeout(() => {
                            hideScanStatus();
                            startScanning();
                        }, 2000);
                    }
                }, 1000);
            } else {
                hideScanStatus();
                startScanning();
            }
        }

        // 显示扫描历史
        function showScanHistory() {
            window.location.href = 'scan-history.html';
        }

        // 显示历史记录
        function showHistory() {
            window.location.href = 'history.html';
        }

        // 解析二维码
        function parseQRCode(code) {
            const mjCodePattern = /^MJ\d{8}\d{3}$/;
            const urlPattern = /https:\/\/mahjongcode\.app\/join\/(MJ\d{8}\d{3})$/;

            let gameId = null;

            if (mjCodePattern.test(code)) {
                gameId = code;
            } else if (urlPattern.test(code)) {
                const match = code.match(urlPattern);
                gameId = match[1];
            }

            return {
                valid: gameId !== null,
                gameId: gameId,
                originalCode: code
            };
        }

        // 保存扫描历史
        function saveScanHistory(code) {
            const history = JSON.parse(localStorage.getItem('scanHistory') || '[]');
            const scanRecord = {
                id: Date.now(),
                code: code,
                timestamp: new Date().toISOString(),
                result: parseQRCode(code).valid ? 'success' : 'failed'
            };

            history.unshift(scanRecord);

            if (history.length > 50) {
                history.splice(50);
            }

            localStorage.setItem('scanHistory', JSON.stringify(history));
        }

        // 根据代码查找游戏
        function findGameByCode(gameId) {
            const gameData = gameDataManager.getGameData();
            if (!gameData || !gameData.games) return null;

            return Object.values(gameData.games).find(game =>
                game.qrCode === gameId
            );
        }

        // 获取用户信息
        function getUserInfo() {
            const saved = localStorage.getItem('userProfile');
            if (saved) {
                const userInfo = JSON.parse(saved);
                userInfo.avatar = '🀄'; // 确保头像是麻将图标
                return userInfo;
            }

            // 默认用户信息
            return {
                name: '麻将玩家',
                avatar: '🀄'
            };
        }

        // 通过代码加入游戏
        async function joinGameByCode(gameId, gameInfo) {
            showScanStatus('正在加入牌局...', 'success');

            if (gameInfo) {
                // 获取用户信息
                const userInfo = getUserInfo();

                // 检查用户是否已经在牌局中
                const existingPlayer = gameInfo.players.find(p => p.name === userInfo.name);

                if (!existingPlayer) {
                    // 将当前用户添加到牌局中
                    const newPlayer = {
                        id: 'p' + (gameInfo.players.length + 1),
                        name: userInfo.name,
                        avatar: userInfo.avatar,
                        totalScore: 0,
                        isOwner: false
                    };

                    gameInfo.players.push(newPlayer);

                    // 更新游戏数据
                    const allData = gameDataManager.getGameData();
                    allData.games[gameInfo.id] = gameInfo;
                    gameDataManager.saveGameData(allData);

                    showToast(`${userInfo.name} 已加入牌局！`, 'success');
                } else {
                    showToast('您已经在此牌局中', 'success');
                }

                gameDataManager.setCurrentGame(gameInfo.id);
                setTimeout(() => {
                    window.location.href = 'game-detail.html';
                }, 1500);
            } else {
                const result = await showConfirm(
                    '牌局不存在',
                    '未找到对应的牌局，是否创建新的牌局？',
                    {
                        confirmText: '创建牌局',
                        cancelText: '重新扫描'
                    }
                );

                if (result) {
                    window.location.href = 'create-game.html';
                } else {
                    restartScanning();
                }
            }
        }

        // 重新开始扫描
        function restartScanning() {
            const frame = document.getElementById('scanFrame');
            frame.classList.remove('qr-detected');
            setTimeout(() => {
                hideScanStatus();
                startScanning();
            }, 500);
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initScanner();
        });

        // 页面失去焦点时停止扫描
        document.addEventListener('visibilitychange', function() {
            if (document.hidden) {
                stopScanning();
            } else if (isScanning === false) {
                setTimeout(() => {
                    initScanner();
                }, 500);
            }
        });
    </script>
</body>
</html>
