<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>麻将码 - 历史记录</title>
    <link rel="stylesheet" href="styles/ios-base.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="js/modal.js"></script>
    <script src="js/gameData.js"></script>
    <style>
        .search-bar {
            padding: 16px;
            background: white;
            border-bottom: 1px solid var(--border-color);
        }

        .search-input {
            width: 100%;
            padding: 12px 16px 12px 40px;
            border: 1px solid var(--border-color);
            border-radius: 12px;
            font-size: 16px;
            background: var(--background-color);
            position: relative;
            transition: all 0.2s ease;
        }

        .search-input:focus {
            outline: none;
            border-color: var(--primary-color);
            background: white;
            box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
        }
        
        .search-icon {
            position: absolute;
            left: 28px;
            top: 28px;
            color: var(--text-secondary);
            font-size: 16px;
        }
        
        .filter-tabs {
            display: flex;
            flex-wrap: wrap;
            background: white;
            padding: 16px;
            border-bottom: 1px solid var(--border-color);
            gap: 8px;
        }
        
        .filter-tab {
            padding: 8px 16px;
            border: 1px solid var(--border-color);
            background: white;
            color: var(--text-secondary);
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            white-space: nowrap;
            border-radius: 20px;
            margin: 0 4px 8px 0;
            transition: all 0.2s ease;
        }

        .filter-tab:hover {
            background: var(--background-color);
            border-color: var(--primary-color);
        }

        .filter-tab.active {
            color: white;
            background: var(--primary-color);
            border-color: var(--primary-color);
        }
        
        .history-list {
            padding: 16px;
        }
        
        .date-group {
            margin-bottom: 24px;
        }
        
        .date-header {
            font-size: 14px;
            font-weight: 600;
            color: var(--text-secondary);
            margin-bottom: 12px;
            padding: 0 8px;
        }
        
        .game-card {
            background: white;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 12px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid rgba(0,0,0,0.05);
        }

        .game-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0,0,0,0.12);
        }

        .game-card:active {
            transform: translateY(0);
        }

        .history-item {
            background: white;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 12px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid rgba(0,0,0,0.05);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .history-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0,0,0,0.12);
        }

        .history-item:active {
            transform: translateY(0);
        }

        .game-info {
            display: flex;
            align-items: center;
            gap: 16px;
            flex: 1;
        }

        .game-avatar {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            background: linear-gradient(135deg, var(--primary-color), #34C759);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
        }

        .game-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 12px;
        }
        
        .game-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 4px;
        }
        
        .game-meta {
            font-size: 13px;
            color: var(--text-secondary);
            display: flex;
            align-items: center;
            gap: 12px;
            margin-top: 6px;
        }

        .game-meta span {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .game-meta i {
            font-size: 12px;
            opacity: 0.7;
        }
        
        .game-result {
            text-align: right;
        }
        
        .result-score {
            font-size: 18px;
            font-weight: 700;
            margin-bottom: 2px;
        }
        
        .result-score.positive {
            color: var(--ios-green);
        }
        
        .result-score.negative {
            color: var(--ios-red);
        }
        
        .result-rank {
            font-size: 12px;
            color: var(--text-secondary);
        }
        
        .game-players {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-top: 8px;
        }
        
        .player-avatar-mini {
            width: 24px;
            height: 24px;
            border-radius: 12px;
            background: var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 10px;
            font-weight: 600;
        }
        
        .player-count {
            font-size: 12px;
            color: var(--text-secondary);
            margin-left: 4px;
        }

        .status-completed,
        .status-ongoing {
            font-size: 11px;
            font-weight: 600;
            padding: 4px 8px;
            border-radius: 12px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-completed {
            background: rgba(52, 199, 89, 0.1);
            color: #34C759;
        }

        .status-ongoing {
            background: rgba(255, 149, 0, 0.1);
            color: #FF9500;
        }
        
        .empty-state {
            text-align: center;
            padding: 80px 20px;
            color: var(--text-secondary);
        }

        .empty-icon {
            font-size: 64px;
            margin-bottom: 24px;
            opacity: 0.3;
            background: linear-gradient(135deg, var(--primary-color), #34C759);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .empty-title {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 12px;
            color: var(--text-primary);
        }

        .empty-subtitle {
            font-size: 16px;
            line-height: 1.5;
            margin-bottom: 32px;
        }

        .empty-action-btn {
            background: linear-gradient(135deg, var(--primary-color), #34C759);
            color: white;
            border: none;
            border-radius: 12px;
            padding: 16px 32px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 16px rgba(0, 122, 255, 0.3);
        }

        .empty-action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 122, 255, 0.4);
        }

        .empty-action-btn:active {
            transform: translateY(0);
        }
        
        .floating-filter {
            position: fixed;
            bottom: 100px;
            right: 20px;
            width: 56px;
            height: 56px;
            background: var(--primary-color);
            border-radius: 28px;
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(46, 125, 50, 0.3);
            transition: all 0.2s;
            z-index: 10;
        }
        
        .floating-filter:hover {
            transform: scale(1.1);
        }
    </style>
</head>
<body>
    <div class="device-container">
        <div class="screen">
            <!-- 状态栏 -->
            <div class="status-bar">
                <div class="status-left">
                    <span>9:41</span>
                </div>
                <div class="status-right">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <div class="battery">
                        <div class="battery-fill"></div>
                    </div>
                </div>
            </div>
            
            <!-- 导航栏 -->
            <div class="nav-bar">
                <a href="index.html" class="nav-button">
                    <i class="fas fa-arrow-left"></i>
                </a>
                <div class="nav-title">历史记录</div>
                <div class="nav-button" style="opacity: 0; pointer-events: none;">
                    <!-- 占位元素，保持布局平衡 -->
                </div>
            </div>
            
            <!-- 安全区域内容 -->
            <div class="safe-area">
                <!-- 搜索栏 -->
                <div class="search-bar">
                    <div style="position: relative;">
                        <i class="fas fa-search search-icon"></i>
                        <input type="text" class="search-input" placeholder="搜索牌局名称或玩家">
                    </div>
                </div>
                
                <!-- 筛选标签 -->
                <div class="filter-tabs">
                    <button class="filter-tab active" onclick="setFilter('all')">全部</button>
                    <button class="filter-tab" onclick="setFilter('win')">胜局</button>
                    <button class="filter-tab" onclick="setFilter('lose')">败局</button>
                    <button class="filter-tab" onclick="setFilter('this-month')">本月</button>
                    <button class="filter-tab" onclick="setFilter('guangdong')">广东麻将</button>
                    <button class="filter-tab" onclick="setFilter('sichuan')">四川麻将</button>
                </div>
                
                <!-- 历史记录列表 -->
                <div class="history-list">
                    <!-- 今天 -->
                    <div class="date-group">
                        <div class="date-header">今天</div>
                        
                        <div class="game-card" onclick="viewGame('game1')">
                            <div class="game-header">
                                <div>
                                    <div class="game-title">周末家庭局</div>
                                    <div class="game-meta">
                                        <span><i class="fas fa-clock"></i> 14:30-16:20</span>
                                        <span><i class="fas fa-map-marker-alt"></i> 家里客厅</span>
                                        <span class="status-ongoing">进行中</span>
                                    </div>
                                </div>
                                <div class="game-result">
                                    <div class="result-score positive">+150</div>
                                    <div class="result-rank">第1名</div>
                                </div>
                            </div>
                            <div class="game-players">
                                <div class="player-avatar-mini">张</div>
                                <div class="player-avatar-mini">李</div>
                                <div class="player-avatar-mini">王</div>
                                <div class="player-avatar-mini">赵</div>
                                <span class="player-count">4人局</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 昨天 -->
                    <div class="date-group">
                        <div class="date-header">昨天</div>
                        
                        <div class="game-card" onclick="viewGame('game2')">
                            <div class="game-header">
                                <div>
                                    <div class="game-title">朋友聚会</div>
                                    <div class="game-meta">
                                        <span><i class="fas fa-clock"></i> 19:45-22:30</span>
                                        <span><i class="fas fa-map-marker-alt"></i> 老王家</span>
                                        <span class="status-completed">已结束</span>
                                    </div>
                                </div>
                                <div class="game-result">
                                    <div class="result-score negative">-80</div>
                                    <div class="result-rank">第3名</div>
                                </div>
                            </div>
                            <div class="game-players">
                                <div class="player-avatar-mini">张</div>
                                <div class="player-avatar-mini">李</div>
                                <div class="player-avatar-mini">王</div>
                                <div class="player-avatar-mini">赵</div>
                                <span class="player-count">4人局 · 12局</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 本周 -->
                    <div class="date-group">
                        <div class="date-header">本周</div>
                        
                        <div class="game-card" onclick="viewGame('game3')">
                            <div class="game-header">
                                <div>
                                    <div class="game-title">麻将馆对局</div>
                                    <div class="game-meta">
                                        <span><i class="fas fa-clock"></i> 周三 15:00-18:00</span>
                                        <span><i class="fas fa-map-marker-alt"></i> 金牌麻将馆</span>
                                        <span class="status-completed">已结束</span>
                                    </div>
                                </div>
                                <div class="game-result">
                                    <div class="result-score positive">+220</div>
                                    <div class="result-rank">第1名</div>
                                </div>
                            </div>
                            <div class="game-players">
                                <div class="player-avatar-mini">张</div>
                                <div class="player-avatar-mini">陈</div>
                                <div class="player-avatar-mini">刘</div>
                                <div class="player-avatar-mini">杨</div>
                                <span class="player-count">4人局 · 16局</span>
                            </div>
                        </div>
                        
                        <div class="game-card" onclick="viewGame('game4')">
                            <div class="game-header">
                                <div>
                                    <div class="game-title">公司团建</div>
                                    <div class="game-meta">
                                        <span><i class="fas fa-clock"></i> 周一 20:00-23:30</span>
                                        <span><i class="fas fa-map-marker-alt"></i> KTV包厢</span>
                                        <span class="status-completed">已结束</span>
                                    </div>
                                </div>
                                <div class="game-result">
                                    <div class="result-score negative">-120</div>
                                    <div class="result-rank">第4名</div>
                                </div>
                            </div>
                            <div class="game-players">
                                <div class="player-avatar-mini">张</div>
                                <div class="player-avatar-mini">小</div>
                                <div class="player-avatar-mini">大</div>
                                <div class="player-avatar-mini">老</div>
                                <span class="player-count">4人局 · 8局</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 更早 -->
                    <div class="date-group">
                        <div class="date-header">更早</div>
                        
                        <div class="game-card" onclick="viewGame('game5')">
                            <div class="game-header">
                                <div>
                                    <div class="game-title">春节家庭赛</div>
                                    <div class="game-meta">
                                        <span><i class="fas fa-clock"></i> 1月28日</span>
                                        <span><i class="fas fa-map-marker-alt"></i> 老家</span>
                                        <span class="status-completed">已结束</span>
                                    </div>
                                </div>
                                <div class="game-result">
                                    <div class="result-score positive">+350</div>
                                    <div class="result-rank">第1名</div>
                                </div>
                            </div>
                            <div class="game-players">
                                <div class="player-avatar-mini">张</div>
                                <div class="player-avatar-mini">爸</div>
                                <div class="player-avatar-mini">妈</div>
                                <div class="player-avatar-mini">弟</div>
                                <span class="player-count">4人局 · 20局</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 悬浮筛选按钮 -->
            <button class="floating-filter" onclick="showAdvancedFilter()">
                <i class="fas fa-filter"></i>
            </button>
            
            <!-- Home指示器 -->
            <div class="home-indicator">
                <div class="home-indicator-bar"></div>
            </div>
        </div>
    </div>
    
    <script>
        function setFilter(type) {
            // 移除所有active类
            document.querySelectorAll('.filter-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // 添加active类到当前标签
            event.target.classList.add('active');
            
            // 这里可以添加筛选逻辑
            console.log('筛选类型:', type);
        }
        
        function viewGame(gameId) {
            // 根据gameId跳转到对应的游戏详情页
            gameDataManager.setCurrentGame(gameId);
            window.location.href = 'game-detail.html';
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('=== 历史记录页面初始化 ===');
            loadHistoryData();

            // 监听数据更新事件
            window.addEventListener('gameDataUpdated', function(event) {
                console.log('历史记录页面检测到游戏数据更新，重新加载历史记录', event.detail);
                loadHistoryData();
            });

            // 监听额外的数据变化事件
            window.addEventListener('mahjongDataChanged', function(event) {
                console.log('历史记录页面检测到麻将数据变化，重新加载历史记录', event.detail);
                loadHistoryData();
            });

            // 监听localStorage变化事件
            window.addEventListener('storage', function(event) {
                if (event.key === 'mahjongGameData') {
                    console.log('历史记录页面检测到localStorage变化，重新加载数据');
                    setTimeout(() => {
                        loadHistoryData();
                    }, 50);
                }
            });

            // 监听页面可见性变化
            document.addEventListener('visibilitychange', function() {
                if (!document.hidden) {
                    console.log('历史记录页面重新可见，刷新数据');
                    setTimeout(() => {
                        loadHistoryData();
                    }, 100);
                }
            });

            // 监听窗口焦点变化
            window.addEventListener('focus', function() {
                console.log('历史记录页面重新获得焦点，刷新数据');
                setTimeout(() => {
                    loadHistoryData();
                }, 100);
            });

            // 定期检查数据变化（作为备用方案）
            let lastCompletedGamesCount = 0;
            setInterval(() => {
                try {
                    const gameData = gameDataManager.getGameData();
                    if (gameData && gameData.games) {
                        const completedGames = Object.values(gameData.games).filter(game => game.status === 'completed');
                        if (completedGames.length !== lastCompletedGamesCount) {
                            console.log('历史记录页面定期检查发现已完成游戏数量变化:', lastCompletedGamesCount, '->', completedGames.length);
                            lastCompletedGamesCount = completedGames.length;
                            loadHistoryData();
                        }
                    }
                } catch (error) {
                    console.error('历史记录页面定期检查数据时发生错误:', error);
                }
            }, 2000); // 每2秒检查一次
        });

        // 加载历史数据
        function loadHistoryData() {
            const gameData = gameDataManager.getGameData();
            if (!gameData || !gameData.games) {
                showEmptyHistory();
                return;
            }

            updateHistoryDisplay(gameData);
        }

        // 更新历史记录显示
        function updateHistoryDisplay(gameData) {
            console.log('=== 更新历史记录显示 ===');
            const games = Object.values(gameData.games);
            console.log('所有游戏数量:', games.length);
            console.log('所有游戏:', games);

            if (games.length === 0) {
                console.log('没有游戏数据，显示空状态');
                showEmptyHistory();
                return;
            }

            // 筛选已完成的游戏
            const completedGames = games.filter(game => game.status === 'completed');
            console.log('已完成游戏数量:', completedGames.length);
            console.log('已完成游戏:', completedGames);

            if (completedGames.length === 0) {
                console.log('没有已完成的游戏，显示空状态');
                showEmptyHistory();
                return;
            }

            // 按结束时间排序（最新的在前）
            completedGames.sort((a, b) => new Date(b.endTime || b.startTime) - new Date(a.endTime || a.startTime));

            const container = document.querySelector('.history-list');
            if (!container) {
                console.error('找不到历史记录容器');
                return;
            }

            // 动态生成历史记录HTML
            container.innerHTML = completedGames.map(game => generateGameHistoryHTML(game)).join('');

            console.log('历史游戏数据已更新:', completedGames);
        }

        // 生成单个游戏历史记录的HTML
        function generateGameHistoryHTML(game) {
            const startTime = new Date(game.startTime);
            const endTime = game.endTime ? new Date(game.endTime) : new Date();

            // 格式化时间
            const timeStr = `${startTime.toLocaleDateString('zh-CN')} ${startTime.toLocaleTimeString('zh-CN', {hour: '2-digit', minute: '2-digit'})}-${endTime.toLocaleTimeString('zh-CN', {hour: '2-digit', minute: '2-digit'})}`;

            // 计算当前用户的成绩（假设第一个玩家是当前用户）
            const currentPlayer = game.players[0];
            const totalScore = currentPlayer.totalScore || 0;
            const scoreClass = totalScore > 0 ? 'positive' : totalScore < 0 ? 'negative' : 'zero';
            const scoreText = totalScore > 0 ? `+${totalScore}` : totalScore.toString();

            // 计算排名
            const sortedPlayers = [...game.players].sort((a, b) => (b.totalScore || 0) - (a.totalScore || 0));
            const rank = sortedPlayers.findIndex(p => p.id === currentPlayer.id) + 1;

            return `
                <div class="history-item" onclick="viewGameDetail('${game.id}')">
                    <div class="game-info">
                        <div class="game-avatar">🀄</div>
                        <div>
                            <div class="game-title">${game.name}</div>
                            <div class="game-meta">
                                <span><i class="fas fa-clock"></i> ${timeStr}</span>
                                <span><i class="fas fa-map-marker-alt"></i> ${game.location || '未知地点'}</span>
                                <span class="status-completed">已结束</span>
                            </div>
                        </div>
                    </div>
                    <div class="game-result">
                        <div class="result-score ${scoreClass}">${scoreText}</div>
                        <div class="result-rank">第${rank}名</div>
                    </div>
                </div>
            `;
        }

        // 显示空状态
        function showEmptyHistory() {
            const container = document.querySelector('.history-list');
            if (container) {
                container.innerHTML = `
                    <div class="empty-state">
                        <div class="empty-icon">🀄</div>
                        <div class="empty-title">还没有历史记录</div>
                        <div class="empty-subtitle">开始你的第一局麻将游戏吧</div>
                        <button class="empty-action-btn" onclick="window.location.href='create-game.html'">
                            <i class="fas fa-plus"></i> 创建牌局
                        </button>
                    </div>
                `;
            }
        }
        
        // 查看游戏详情
        function viewGameDetail(gameId) {
            if (gameId) {
                // 设置为当前游戏并跳转到详情页
                gameDataManager.setCurrentGame(gameId);
                window.location.href = 'game-detail.html';
            } else {
                // 兼容原有调用
                window.location.href = 'game-detail.html';
            }
        }

        async function showAdvancedFilter() {
            const options = ['按日期范围', '按积分范围', '按对手筛选', '按地点筛选'];
            const choice = await showSelect(
                '高级筛选',
                '选择筛选条件：',
                options
            );

            if (choice !== null) {
                showToast(`正在设置${options[choice]}...`, 'normal');
            }
        }
    </script>
</body>
</html>
