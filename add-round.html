<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>麻将码 - 记录局次</title>
    <link rel="stylesheet" href="styles/ios-base.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="js/modal.js"></script>
    <script src="js/gameData.js"></script>
    <style>
        .round-header {
            background: linear-gradient(135deg, #2E7D32 0%, #4CAF50 100%);
            color: white;
            padding: 24px 16px;
            text-align: center;
        }
        
        .round-title {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 8px;
        }
        
        .round-info {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .form-section {
            padding: 24px 16px;
        }
        
        .winner-selection {
            margin-bottom: 32px;
        }
        
        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 16px;
        }
        
        .winner-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
        }
        
        .winner-option {
            background: white;
            border: 2px solid var(--border-color);
            border-radius: 16px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.2s;
            position: relative;
        }
        
        .winner-option.selected {
            border-color: var(--primary-color);
            background: linear-gradient(135deg, #E8F5E8 0%, #C8E6C9 100%);
        }
        
        .winner-option.selected::after {
            content: '✓';
            position: absolute;
            top: 8px;
            right: 8px;
            width: 20px;
            height: 20px;
            background: var(--primary-color);
            color: white;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: 600;
        }
        
        .winner-avatar {
            width: 48px;
            height: 48px;
            border-radius: 24px;
            background: var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
            font-weight: 600;
            margin: 0 auto 8px;
        }
        
        .winner-name {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
        }
        
        .score-input-section {
            margin-bottom: 32px;
        }
        
        .score-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
        }
        
        .score-input-card {
            background: white;
            border-radius: 16px;
            padding: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .score-input-card:hover {
            box-shadow: 0 4px 16px rgba(0,0,0,0.15);
            transform: translateY(-2px);
        }

        .score-input-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .score-input-card:hover::before {
            opacity: 1;
        }
        
        .player-info {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 12px;
        }
        
        .player-avatar-small {
            width: 36px;
            height: 36px;
            border-radius: 18px;
            background: var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }
        
        .player-name-small {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
        }
        
        .score-input {
            width: 100%;
            height: 48px;
            padding: 12px 16px;
            border: 2px solid var(--border-color);
            border-radius: 12px;
            font-size: 18px;
            font-weight: 600;
            text-align: center;
            background: var(--background-color);
            transition: border-color 0.2s;
            box-sizing: border-box;
        }
        
        .score-input:focus {
            outline: none;
            border-color: var(--primary-color);
            background: white;
        }
        
        .score-input.winner {
            border-color: var(--accent-color);
            background: #FFF3E0;
            color: var(--accent-color);
        }
        
        .quick-scores {
            margin-top: 10px;
        }

        .quick-scores-section {
            display: flex;
            flex-direction: column;
            gap: 6px;
        }

        .quick-scores-row {
            display: flex;
            gap: 6px;
            justify-content: center;
        }

        .quick-scores-label {
            font-size: 11px;
            color: var(--text-secondary);
            text-align: center;
            margin-bottom: 2px;
            font-weight: 500;
        }

        .quick-score-btn {
            flex: 1;
            height: 36px;
            padding: 8px;
            border: 1.5px solid;
            border-radius: 10px;
            font-size: 13px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            min-width: 44px;
            text-align: center;
            box-sizing: border-box;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .quick-score-btn.positive {
            background: linear-gradient(135deg, #E8F5E8 0%, #C8E6C9 100%);
            border-color: #4CAF50;
            color: #2E7D32;
        }

        .quick-score-btn.positive:hover {
            background: linear-gradient(135deg, #4CAF50 0%, #66BB6A 100%);
            color: white;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
        }

        .quick-score-btn.negative {
            background: linear-gradient(135deg, #FFEBEE 0%, #FFCDD2 100%);
            border-color: #F44336;
            color: #C62828;
        }

        .quick-score-btn.negative:hover {
            background: linear-gradient(135deg, #F44336 0%, #EF5350 100%);
            color: white;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(244, 67, 54, 0.3);
        }

        .quick-score-btn:active {
            transform: translateY(0);
            box-shadow: 0 2px 6px rgba(0,0,0,0.2);
        }

        .quick-score-btn.clicked {
            animation: scoreButtonClick 0.3s ease;
        }

        @keyframes scoreButtonClick {
            0% { transform: scale(1); }
            50% { transform: scale(0.95); }
            100% { transform: scale(1); }
        }

        .clear-score-btn {
            width: 100%;
            height: 32px;
            padding: 6px;
            margin-top: 8px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            background: #F5F5F5;
            color: var(--text-secondary);
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s;
            box-sizing: border-box;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .clear-score-btn:hover {
            background: var(--ios-gray);
            color: white;
            border-color: var(--ios-gray);
        }
        
        .round-type-section {
            margin-bottom: 32px;
        }
        
        .type-options {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }
        
        .type-option {
            padding: 12px 16px;
            border: 2px solid var(--border-color);
            border-radius: 20px;
            background: white;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .type-option.selected {
            border-color: var(--primary-color);
            background: var(--primary-color);
            color: white;
        }
        
        .notes-section {
            margin-bottom: 32px;
        }
        
        .notes-input {
            width: 100%;
            min-height: 80px;
            padding: 16px;
            border: 2px solid var(--border-color);
            border-radius: 12px;
            font-size: 16px;
            background: white;
            resize: vertical;
            transition: border-color 0.2s;
            box-sizing: border-box;
        }
        
        .notes-input:focus {
            outline: none;
            border-color: var(--primary-color);
        }
        
        .action-buttons {
            display: flex;
            gap: 12px;
            padding: 16px;
            background: white;
            border-top: 1px solid var(--border-color);
            position: sticky;
            bottom: 0;
        }
        
        .btn-cancel {
            flex: 1;
            padding: 16px;
            border: 2px solid var(--ios-gray);
            border-radius: 12px;
            background: transparent;
            color: var(--ios-gray);
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .btn-cancel:hover {
            background: var(--ios-gray);
            color: white;
        }
        
        .score-summary {
            background: var(--background-color);
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 24px;
        }
        
        .summary-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 12px;
            text-align: center;
        }
        
        .summary-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 8px;
        }
        
        .summary-item {
            text-align: center;
            padding: 8px;
            background: white;
            border-radius: 8px;
        }
        
        .summary-name {
            font-size: 12px;
            color: var(--text-secondary);
            margin-bottom: 4px;
        }
        
        .summary-score {
            font-size: 16px;
            font-weight: 600;
        }
        
        .summary-score.positive {
            color: var(--ios-green);
        }
        
        .summary-score.negative {
            color: var(--ios-red);
        }
        
        .summary-score.zero {
            color: var(--text-secondary);
        }
    </style>
</head>
<body>
    <div class="device-container">
        <div class="screen">
            <!-- 状态栏 -->
            <div class="status-bar">
                <div class="status-left">
                    <span>9:41</span>
                </div>
                <div class="status-right">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <div class="battery">
                        <div class="battery-fill"></div>
                    </div>
                </div>
            </div>
            
            <!-- 导航栏 -->
            <div class="nav-bar">
                <a href="game-detail.html" class="nav-button">
                    <i class="fas fa-times"></i>
                </a>
                <div class="nav-title">记录局次</div>
                <button class="nav-button" onclick="saveRound()">保存</button>
            </div>
            
            <!-- 安全区域内容 -->
            <div class="safe-area">
                <!-- 局次头部 -->
                <div class="round-header">
                    <div class="round-title">第4局</div>
                    <div class="round-info">周末家庭局 · 16:10</div>
                </div>
                
                <div class="form-section">
                    <!-- 胡牌者选择 -->
                    <div class="winner-selection">
                        <h2 class="section-title">谁胡牌了？</h2>
                        <div class="winner-grid">
                            <div class="winner-option" onclick="selectWinner(this, '张三')">
                                <div class="winner-avatar">张</div>
                                <div class="winner-name">张三</div>
                            </div>
                            
                            <div class="winner-option" onclick="selectWinner(this, '李四')">
                                <div class="winner-avatar">李</div>
                                <div class="winner-name">李四</div>
                            </div>
                            
                            <div class="winner-option" onclick="selectWinner(this, '王五')">
                                <div class="winner-avatar">王</div>
                                <div class="winner-name">王五</div>
                            </div>
                            
                            <div class="winner-option" onclick="selectWinner(this, '赵六')">
                                <div class="winner-avatar">赵</div>
                                <div class="winner-name">赵六</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 分数输入 -->
                    <div class="score-input-section">
                        <h2 class="section-title">输入分数</h2>
                        <div class="score-grid">
                            <div class="score-input-card">
                                <div class="player-info">
                                    <div class="player-avatar-small">张</div>
                                    <div class="player-name-small">张三</div>
                                </div>
                                <input type="number" class="score-input" placeholder="0" id="score-zhang">
                                <div class="quick-scores">
                                    <div class="quick-scores-section">
                                        <div class="quick-scores-label">常用正分</div>
                                        <div class="quick-scores-row">
                                            <button class="quick-score-btn positive" onclick="setScoreWithAnimation('score-zhang', 80, this)">+80</button>
                                            <button class="quick-score-btn positive" onclick="setScoreWithAnimation('score-zhang', 40, this)">+40</button>
                                        </div>
                                        <div class="quick-scores-row">
                                            <button class="quick-score-btn negative" onclick="setScoreWithAnimation('score-zhang', -20, this)">-20</button>
                                            <button class="quick-score-btn negative" onclick="setScoreWithAnimation('score-zhang', -30, this)">-30</button>
                                        </div>
                                        <button class="clear-score-btn" onclick="clearScore('score-zhang')">清除</button>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="score-input-card">
                                <div class="player-info">
                                    <div class="player-avatar-small">李</div>
                                    <div class="player-name-small">李四</div>
                                </div>
                                <input type="number" class="score-input" placeholder="0" id="score-li">
                                <div class="quick-scores">
                                    <div class="quick-scores-section">
                                        <div class="quick-scores-label">常用分数</div>
                                        <div class="quick-scores-row">
                                            <button class="quick-score-btn positive" onclick="setScoreWithAnimation('score-li', 80, this)">+80</button>
                                            <button class="quick-score-btn positive" onclick="setScoreWithAnimation('score-li', 40, this)">+40</button>
                                        </div>
                                        <div class="quick-scores-row">
                                            <button class="quick-score-btn negative" onclick="setScoreWithAnimation('score-li', -20, this)">-20</button>
                                            <button class="quick-score-btn negative" onclick="setScoreWithAnimation('score-li', -30, this)">-30</button>
                                        </div>
                                        <button class="clear-score-btn" onclick="clearScore('score-li')">清除</button>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="score-input-card">
                                <div class="player-info">
                                    <div class="player-avatar-small">王</div>
                                    <div class="player-name-small">王五</div>
                                </div>
                                <input type="number" class="score-input" placeholder="0" id="score-wang">
                                <div class="quick-scores">
                                    <div class="quick-scores-section">
                                        <div class="quick-scores-label">常用分数</div>
                                        <div class="quick-scores-row">
                                            <button class="quick-score-btn positive" onclick="setScoreWithAnimation('score-wang', 80, this)">+80</button>
                                            <button class="quick-score-btn positive" onclick="setScoreWithAnimation('score-wang', 40, this)">+40</button>
                                        </div>
                                        <div class="quick-scores-row">
                                            <button class="quick-score-btn negative" onclick="setScoreWithAnimation('score-wang', -20, this)">-20</button>
                                            <button class="quick-score-btn negative" onclick="setScoreWithAnimation('score-wang', -30, this)">-30</button>
                                        </div>
                                        <button class="clear-score-btn" onclick="clearScore('score-wang')">清除</button>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="score-input-card">
                                <div class="player-info">
                                    <div class="player-avatar-small">赵</div>
                                    <div class="player-name-small">赵六</div>
                                </div>
                                <input type="number" class="score-input" placeholder="0" id="score-zhao">
                                <div class="quick-scores">
                                    <div class="quick-scores-section">
                                        <div class="quick-scores-label">常用分数</div>
                                        <div class="quick-scores-row">
                                            <button class="quick-score-btn positive" onclick="setScoreWithAnimation('score-zhao', 80, this)">+80</button>
                                            <button class="quick-score-btn positive" onclick="setScoreWithAnimation('score-zhao', 40, this)">+40</button>
                                        </div>
                                        <div class="quick-scores-row">
                                            <button class="quick-score-btn negative" onclick="setScoreWithAnimation('score-zhao', -20, this)">-20</button>
                                            <button class="quick-score-btn negative" onclick="setScoreWithAnimation('score-zhao', -30, this)">-30</button>
                                        </div>
                                        <button class="clear-score-btn" onclick="clearScore('score-zhao')">清除</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 分数汇总 -->
                    <div class="score-summary">
                        <div class="summary-title">分数汇总 (总和应为0)</div>
                        <div class="summary-grid">
                            <div class="summary-item">
                                <div class="summary-name">张三</div>
                                <div class="summary-score zero" id="summary-zhang">0</div>
                            </div>
                            <div class="summary-item">
                                <div class="summary-name">李四</div>
                                <div class="summary-score zero" id="summary-li">0</div>
                            </div>
                            <div class="summary-item">
                                <div class="summary-name">王五</div>
                                <div class="summary-score zero" id="summary-wang">0</div>
                            </div>
                            <div class="summary-item">
                                <div class="summary-name">赵六</div>
                                <div class="summary-score zero" id="summary-zhao">0</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 胡牌类型 -->
                    <div class="round-type-section">
                        <h2 class="section-title">胡牌类型</h2>
                        <div class="type-options">
                            <div class="type-option selected" onclick="selectType(this)">自摸</div>
                            <div class="type-option" onclick="selectType(this)">点炮</div>
                            <div class="type-option" onclick="selectType(this)">七对</div>
                            <div class="type-option" onclick="selectType(this)">杠上花</div>
                            <div class="type-option" onclick="selectType(this)">抢杠胡</div>
                        </div>
                    </div>
                    
                    <!-- 备注 -->
                    <div class="notes-section">
                        <h2 class="section-title">备注 (可选)</h2>
                        <textarea class="notes-input" placeholder="记录这局的特殊情况或有趣时刻..."></textarea>
                    </div>
                </div>
                
                <!-- 操作按钮 -->
                <div class="action-buttons">
                    <button class="btn-cancel" onclick="cancelRound()">取消</button>
                    <button class="btn-primary" onclick="saveRound()">保存局次</button>
                </div>
            </div>
            
            <!-- Home指示器 -->
            <div class="home-indicator">
                <div class="home-indicator-bar"></div>
            </div>
        </div>
    </div>
    
    <script>
        let selectedWinner = null;
        let currentGame = null;

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('添加局次页面加载完成');
            console.log('gameDataManager是否存在:', typeof gameDataManager !== 'undefined');
            loadCurrentGame();
        });

        // 加载当前游戏数据
        function loadCurrentGame() {
            console.log('=== 加载当前游戏数据 ===');

            currentGame = gameDataManager.getCurrentGame();
            console.log('当前游戏:', currentGame);

            if (!currentGame) {
                console.log('没有找到当前游戏，尝试初始化默认数据');

                // 尝试初始化默认数据
                gameDataManager.clearAllData();
                gameDataManager.init();
                currentGame = gameDataManager.getCurrentGame();

                if (!currentGame) {
                    console.error('初始化后仍然没有找到游戏数据');
                    showAlert('错误', '未找到当前游戏数据').then(() => {
                        window.location.href = 'index.html';
                    });
                    return;
                } else {
                    console.log('初始化成功，当前游戏:', currentGame);
                }
            }

            // 检查游戏状态
            if (currentGame.status === 'completed') {
                console.log('游戏已结束，不允许添加新局次');
                showAlert('游戏已结束', '此游戏已经结束，无法添加新的局次记录。').then(() => {
                    window.location.href = 'game-detail.html';
                });
                return;
            }

            // 更新页面标题
            const roundNumber = currentGame.rounds.length + 1;
            const titleElement = document.querySelector('.round-title');
            const infoElement = document.querySelector('.round-info');

            if (titleElement) {
                titleElement.textContent = `第${roundNumber}局`;
                console.log('更新标题为:', titleElement.textContent);
            } else {
                console.warn('找不到 .round-title 元素');
            }

            if (infoElement) {
                infoElement.textContent = `${currentGame.name} · ${new Date().toLocaleTimeString('zh-CN', {hour: '2-digit', minute: '2-digit'})}`;
                console.log('更新信息为:', infoElement.textContent);
            } else {
                console.warn('找不到 .round-info 元素');
            }

            // 动态生成玩家选项
            generatePlayerOptions();
        }

        // 动态生成玩家选项
        function generatePlayerOptions() {
            if (!currentGame || !currentGame.players) {
                console.error('没有玩家数据');
                return;
            }

            console.log('生成玩家选项，玩家数据:', currentGame.players);

            // 生成胡牌者选择
            const winnerGrid = document.querySelector('.winner-grid');
            if (winnerGrid) {
                winnerGrid.innerHTML = '';
                currentGame.players.forEach(player => {
                    const winnerOption = document.createElement('div');
                    winnerOption.className = 'winner-option';
                    winnerOption.onclick = () => selectWinner(winnerOption, player.name);
                    winnerOption.innerHTML = `
                        <div class="winner-avatar">${player.avatar || player.name.charAt(0)}</div>
                        <div class="winner-name">${player.name}</div>
                    `;
                    winnerGrid.appendChild(winnerOption);
                });
            }

            // 生成分数输入
            const scoreGrid = document.querySelector('.score-grid');
            if (scoreGrid) {
                scoreGrid.innerHTML = '';
                currentGame.players.forEach((player, index) => {
                    const scoreCard = document.createElement('div');
                    scoreCard.className = 'score-input-card';
                    const playerId = `score-${player.id || 'p' + (index + 1)}`;
                    scoreCard.innerHTML = `
                        <div class="player-info">
                            <div class="player-avatar-small">${player.avatar || player.name.charAt(0)}</div>
                            <div class="player-name-small">${player.name}</div>
                        </div>
                        <input type="number" class="score-input" placeholder="0" id="${playerId}" oninput="updateScoreSummary()">
                        <div class="quick-scores">
                            <div class="quick-scores-section">
                                <div class="quick-scores-label">常用分数</div>
                                <div class="quick-scores-row">
                                    <button class="quick-score-btn positive" onclick="setScoreWithAnimation('${playerId}', 80, this)">+80</button>
                                    <button class="quick-score-btn positive" onclick="setScoreWithAnimation('${playerId}', 40, this)">+40</button>
                                </div>
                                <div class="quick-scores-row">
                                    <button class="quick-score-btn negative" onclick="setScoreWithAnimation('${playerId}', -20, this)">-20</button>
                                    <button class="quick-score-btn negative" onclick="setScoreWithAnimation('${playerId}', -30, this)">-30</button>
                                </div>
                                <button class="clear-score-btn" onclick="clearScore('${playerId}')">清除</button>
                            </div>
                        </div>
                    `;
                    scoreGrid.appendChild(scoreCard);
                });
            }

            // 生成分数汇总
            const summaryGrid = document.querySelector('.summary-grid');
            if (summaryGrid) {
                summaryGrid.innerHTML = '';
                currentGame.players.forEach((player, index) => {
                    const summaryItem = document.createElement('div');
                    summaryItem.className = 'summary-item';
                    const summaryId = `summary-${player.id || 'p' + (index + 1)}`;
                    summaryItem.innerHTML = `
                        <div class="summary-name">${player.name}</div>
                        <div class="summary-score zero" id="${summaryId}">0</div>
                    `;
                    summaryGrid.appendChild(summaryItem);
                });
            }

            console.log('玩家选项生成完成');
        }
        
        function selectWinner(element, name) {
            console.log('选择胡牌者:', name);

            // 移除其他选中状态
            document.querySelectorAll('.winner-option').forEach(option => {
                option.classList.remove('selected');
            });

            // 选中当前选项
            element.classList.add('selected');
            selectedWinner = name;

            console.log('selectedWinner已设置为:', selectedWinner);

            // 更新对应的分数输入框样式
            updateWinnerScoreInput(name);
        }

        function updateWinnerScoreInput(winnerName) {
            document.querySelectorAll('.score-input').forEach(input => {
                input.classList.remove('winner');
            });

            // 动态查找对应的玩家输入框
            if (currentGame && currentGame.players) {
                const player = currentGame.players.find(p => p.name === winnerName);
                if (player) {
                    const playerId = `score-${player.id || 'p' + (currentGame.players.indexOf(player) + 1)}`;
                    const inputElement = document.getElementById(playerId);
                    if (inputElement) {
                        inputElement.classList.add('winner');
                    }
                }
            }
        }
        
        function setScore(inputId, score) {
            document.getElementById(inputId).value = score;
            updateScoreSummary();
        }

        function setScoreWithAnimation(inputId, score, buttonElement) {
            // 设置分数
            document.getElementById(inputId).value = score;
            updateScoreSummary();

            // 添加点击动画
            if (buttonElement) {
                buttonElement.classList.add('clicked');
                setTimeout(() => {
                    buttonElement.classList.remove('clicked');
                }, 300);
            }

            // 添加输入框闪烁效果
            const inputElement = document.getElementById(inputId);
            if (inputElement) {
                inputElement.style.transition = 'all 0.3s ease';
                inputElement.style.transform = 'scale(1.05)';
                inputElement.style.boxShadow = score > 0 ?
                    '0 0 15px rgba(76, 175, 80, 0.5)' :
                    '0 0 15px rgba(244, 67, 54, 0.5)';

                setTimeout(() => {
                    inputElement.style.transform = 'scale(1)';
                    inputElement.style.boxShadow = '';
                }, 300);
            }
        }

        function clearScore(inputId) {
            const inputElement = document.getElementById(inputId);
            if (inputElement) {
                inputElement.value = '';
                updateScoreSummary();

                // 添加清除动画效果
                inputElement.style.transition = 'all 0.3s ease';
                inputElement.style.transform = 'scale(0.95)';
                inputElement.style.opacity = '0.5';

                setTimeout(() => {
                    inputElement.style.transform = 'scale(1)';
                    inputElement.style.opacity = '1';
                }, 200);
            }
        }

        function updateScoreSummary() {
            if (!currentGame || !currentGame.players) {
                return;
            }

            currentGame.players.forEach((player, index) => {
                const playerId = `score-${player.id || 'p' + (index + 1)}`;
                const summaryId = `summary-${player.id || 'p' + (index + 1)}`;

                const scoreInput = document.getElementById(playerId);
                const summaryElement = document.getElementById(summaryId);

                if (scoreInput && summaryElement) {
                    const score = parseInt(scoreInput.value) || 0;
                    updateSummaryDisplay(summaryId, score);
                }
            });
        }
        
        function updateSummaryDisplay(elementId, score) {
            const element = document.getElementById(elementId);
            element.textContent = score > 0 ? `+${score}` : score.toString();
            
            element.className = 'summary-score';
            if (score > 0) {
                element.classList.add('positive');
            } else if (score < 0) {
                element.classList.add('negative');
            } else {
                element.classList.add('zero');
            }
        }
        
        function selectType(element) {
            document.querySelectorAll('.type-option').forEach(option => {
                option.classList.remove('selected');
            });
            element.classList.add('selected');
        }
        
        async function cancelRound() {
            const result = await showConfirm(
                '取消记录',
                '确认取消记录？未保存的数据将丢失。',
                {
                    confirmText: '确认取消',
                    cancelText: '继续编辑',
                    confirmClass: 'danger'
                }
            );

            if (result) {
                window.location.href = 'game-detail.html';
            }
        }

        async function saveRound() {
            console.log('=== 开始保存局次 ===');
            console.log('selectedWinner:', selectedWinner);

            // 检查游戏状态
            if (!currentGame) {
                console.error('没有当前游戏数据');
                await showAlert('错误', '没有找到当前游戏数据');
                return;
            }

            if (currentGame.status === 'completed') {
                console.log('游戏已结束，不允许保存新局次');
                await showAlert('游戏已结束', '此游戏已经结束，无法添加新的局次记录。');
                return;
            }

            if (!selectedWinner) {
                console.log('没有选择胡牌者，显示提示');
                await showAlert('提示', '请选择胡牌者');
                return;
            }

            // 动态获取所有玩家的分数
            const scores = {};
            let total = 0;

            if (!currentGame.players) {
                console.error('没有玩家数据');
                await showAlert('错误', '没有找到玩家数据');
                return;
            }

            currentGame.players.forEach((player, index) => {
                const playerId = `score-${player.id || 'p' + (index + 1)}`;
                const scoreInput = document.getElementById(playerId);
                const score = parseInt(scoreInput ? scoreInput.value : '0') || 0;
                scores[player.name] = score;
                total += score;
                console.log(`${player.name} (${playerId}): ${score}`);
            });

            console.log('解析后的分数:', scores);
            console.log('分数总和:', total);

            if (total !== 0) {
                console.log('分数总和不为0，显示错误提示');
                await showAlert('输入错误', `分数总和必须为0，当前为${total}`);
                return;
            }

            // 获取胡牌类型
            const selectedType = document.querySelector('.type-option.selected');
            const roundType = selectedType ? selectedType.textContent : '自摸';
            console.log('胡牌类型:', roundType, '选中元素:', selectedType);

            // 获取备注
            const notesElement = document.querySelector('.notes-input');
            const notes = notesElement ? notesElement.value.trim() : '';
            console.log('备注:', notes, '备注元素:', notesElement);

            // 保存局次数据
            const roundData = {
                winner: selectedWinner,
                scores: scores,
                roundType: roundType,
                notes: notes
            };

            console.log('准备保存的局次数据:', roundData);

            try {
                console.log('=== 开始调用gameDataManager.addRound ===');
                console.log('gameDataManager是否存在:', typeof gameDataManager !== 'undefined');

                if (typeof gameDataManager === 'undefined') {
                    console.error('gameDataManager未定义');
                    await showAlert('保存失败', 'gameDataManager未定义，请刷新页面重试');
                    return;
                }

                console.log('当前游戏状态:', gameDataManager.getCurrentGame());
                console.log('准备保存的数据:', roundData);

                const success = gameDataManager.addRound(roundData);
                console.log('addRound返回结果:', success);

                if (success) {
                    const updatedGame = gameDataManager.getCurrentGame();
                    console.log('保存后的游戏状态:', updatedGame);
                    console.log('保存后的局次数量:', updatedGame ? updatedGame.rounds.length : 'null');

                    // 检查localStorage
                    const storageData = localStorage.getItem('mahjongGameData');
                    console.log('localStorage中的数据:', storageData);

                    showToast('局次记录已保存', 'success');

                    // 触发数据更新事件
                    gameDataManager.triggerDataUpdate();

                    // 延迟跳转，让用户看到成功提示
                    setTimeout(() => {
                        console.log('准备跳转到牌局详情页面');
                        window.location.replace('game-detail.html?updated=' + Date.now());
                    }, 1000);
                } else {
                    console.error('addRound返回false');
                    await showAlert('保存失败', '无法保存局次记录，请重试');
                }
            } catch (error) {
                console.error('保存过程中出现错误:', error);
                console.error('错误堆栈:', error.stack);
                await showAlert('保存失败', '保存过程中出现错误: ' + error.message);
            }
        }
        
        // 监听分数输入变化
        document.querySelectorAll('.score-input').forEach(input => {
            input.addEventListener('input', updateSummary);
        });
    </script>
</body>
</html>
