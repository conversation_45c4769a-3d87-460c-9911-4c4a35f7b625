<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>麻将码 - 分享二维码</title>
    <link rel="stylesheet" href="styles/ios-base.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="js/modal.js"></script>
    <style>
        .qr-header {
            background: linear-gradient(135deg, #2E7D32 0%, #4CAF50 100%);
            color: white;
            padding: 32px 16px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .qr-header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><text y="50" font-size="25" fill="rgba(255,255,255,0.1)">📱</text></svg>') repeat;
            background-size: 60px 60px;
            animation: float 20s linear infinite;
        }
        
        .qr-content {
            position: relative;
            z-index: 2;
        }
        
        .qr-title {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 8px;
        }
        
        .qr-subtitle {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .qr-display {
            padding: 32px 16px;
            text-align: center;
        }
        
        .qr-container {
            background: white;
            border-radius: 20px;
            padding: 32px;
            margin: 0 auto 24px;
            box-shadow: 0 8px 24px rgba(0,0,0,0.15);
            max-width: 280px;
            position: relative;
        }
        
        .qr-code-display {
            width: 200px;
            height: 200px;
            background: #f8f8f8;
            border-radius: 12px;
            margin: 0 auto 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }
        
        .qr-pattern {
            width: 100%;
            height: 100%;
            background: 
                radial-gradient(circle at 20% 20%, #000 2px, transparent 2px),
                radial-gradient(circle at 80% 20%, #000 2px, transparent 2px),
                radial-gradient(circle at 20% 80%, #000 2px, transparent 2px),
                radial-gradient(circle at 80% 80%, #000 2px, transparent 2px),
                linear-gradient(90deg, #000 1px, transparent 1px),
                linear-gradient(0deg, #000 1px, transparent 1px);
            background-size: 
                20px 20px,
                20px 20px,
                20px 20px,
                20px 20px,
                10px 10px,
                10px 10px;
            opacity: 0.8;
        }
        
        .qr-logo {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 40px;
            height: 40px;
            background: var(--primary-color);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
            border: 3px solid white;
        }
        
        .qr-info {
            text-align: center;
        }
        
        .game-name {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 4px;
        }
        
        .game-id {
            font-size: 12px;
            color: var(--text-secondary);
            font-family: monospace;
            background: var(--background-color);
            padding: 4px 8px;
            border-radius: 6px;
            display: inline-block;
        }
        
        .qr-actions {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            padding: 0 16px 24px;
        }
        
        .action-btn {
            padding: 16px;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }
        
        .btn-save {
            background: var(--ios-blue);
            color: white;
        }
        
        .btn-copy {
            background: var(--ios-green);
            color: white;
        }
        
        .share-options {
            padding: 0 16px 24px;
        }
        
        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 16px;
            text-align: center;
        }
        
        .share-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 16px;
        }
        
        .share-item {
            text-align: center;
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .share-item:hover {
            transform: translateY(-2px);
        }
        
        .share-icon {
            width: 56px;
            height: 56px;
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 8px;
            font-size: 28px;
            color: white;
        }
        
        .icon-wechat {
            background: #07C160;
        }
        
        .icon-qq {
            background: #12B7F5;
        }
        
        .icon-weibo {
            background: #E6162D;
        }
        
        .icon-more {
            background: var(--ios-gray);
        }
        
        .share-label {
            font-size: 12px;
            color: var(--text-secondary);
            font-weight: 500;
        }
        
        .qr-settings {
            padding: 0 16px 24px;
        }
        
        .settings-card {
            background: white;
            border-radius: 12px;
            padding: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .setting-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 0;
            border-bottom: 1px solid var(--border-color);
        }
        
        .setting-item:last-child {
            border-bottom: none;
        }
        
        .setting-label {
            font-size: 16px;
            color: var(--text-primary);
        }
        
        .setting-value {
            font-size: 14px;
            color: var(--text-secondary);
            cursor: pointer;
        }
        
        .qr-stats {
            background: var(--background-color);
            border-radius: 12px;
            padding: 16px;
            margin: 0 16px 24px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 16px;
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-value {
            font-size: 20px;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 4px;
        }
        
        .stat-label {
            font-size: 12px;
            color: var(--text-secondary);
        }
        
        .refresh-btn {
            position: absolute;
            top: 8px;
            right: 8px;
            width: 32px;
            height: 32px;
            border-radius: 16px;
            background: rgba(0,0,0,0.1);
            border: none;
            color: var(--text-secondary);
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .refresh-btn:hover {
            background: rgba(0,0,0,0.2);
            transform: rotate(180deg);
        }
    </style>
</head>
<body>
    <div class="device-container">
        <div class="screen">
            <!-- 状态栏 -->
            <div class="status-bar">
                <div class="status-left">
                    <span>9:41</span>
                </div>
                <div class="status-right">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <div class="battery">
                        <div class="battery-fill"></div>
                    </div>
                </div>
            </div>
            
            <!-- 导航栏 -->
            <div class="nav-bar">
                <a href="game-detail.html" class="nav-button">
                    <i class="fas fa-arrow-left"></i>
                </a>
                <div class="nav-title">分享二维码</div>
                <button class="nav-button" onclick="showSettings()">
                    <i class="fas fa-cog"></i>
                </button>
            </div>
            
            <!-- 安全区域内容 -->
            <div class="safe-area">
                <!-- 头部信息 -->
                <div class="qr-header">
                    <div class="qr-content">
                        <div class="qr-title">牌局二维码</div>
                        <div class="qr-subtitle">扫描即可快速加入牌局</div>
                    </div>
                </div>
                
                <!-- 二维码显示 -->
                <div class="qr-display">
                    <div class="qr-container">
                        <button class="refresh-btn" onclick="refreshQR()">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                        <div class="qr-code-display">
                            <div class="qr-pattern"></div>
                            <div class="qr-logo">🀄</div>
                        </div>
                        <div class="qr-info">
                            <div class="game-name">周末家庭局</div>
                            <div class="game-id">MJ20240118001</div>
                        </div>
                    </div>
                </div>
                
                <!-- 统计信息 -->
                <div class="qr-stats">
                    <div class="stats-grid">
                        <div class="stat-item">
                            <div class="stat-value">15</div>
                            <div class="stat-label">扫描次数</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">4</div>
                            <div class="stat-label">已加入</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">2h</div>
                            <div class="stat-label">有效期</div>
                        </div>
                    </div>
                </div>
                
                <!-- 快捷操作 -->
                <div class="qr-actions">
                    <button class="action-btn btn-save" onclick="saveQR()">
                        <i class="fas fa-download"></i>
                        保存图片
                    </button>
                    <button class="action-btn btn-copy" onclick="copyLink()">
                        <i class="fas fa-link"></i>
                        复制链接
                    </button>
                </div>
                
                <!-- 分享选项 -->
                <div class="share-options">
                    <h2 class="section-title">分享到</h2>
                    <div class="share-grid">
                        <div class="share-item" onclick="shareToWechat()">
                            <div class="share-icon icon-wechat">
                                <i class="fab fa-weixin"></i>
                            </div>
                            <div class="share-label">微信</div>
                        </div>
                        
                        <div class="share-item" onclick="shareToQQ()">
                            <div class="share-icon icon-qq">
                                <i class="fab fa-qq"></i>
                            </div>
                            <div class="share-label">QQ</div>
                        </div>
                        
                        <div class="share-item" onclick="shareToWeibo()">
                            <div class="share-icon icon-weibo">
                                <i class="fab fa-weibo"></i>
                            </div>
                            <div class="share-label">微博</div>
                        </div>
                        
                        <div class="share-item" onclick="shareMore()">
                            <div class="share-icon icon-more">
                                <i class="fas fa-ellipsis-h"></i>
                            </div>
                            <div class="share-label">更多</div>
                        </div>
                    </div>
                </div>
                
                <!-- 设置选项 -->
                <div class="qr-settings">
                    <h2 class="section-title">二维码设置</h2>
                    <div class="settings-card">
                        <div class="setting-item">
                            <div class="setting-label">有效期</div>
                            <div class="setting-value" onclick="setExpiry()">2小时 ></div>
                        </div>
                        <div class="setting-item">
                            <div class="setting-label">最大人数</div>
                            <div class="setting-value" onclick="setMaxPlayers()">4人 ></div>
                        </div>
                        <div class="setting-item">
                            <div class="setting-label">加入权限</div>
                            <div class="setting-value" onclick="setPermission()">公开 ></div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Home指示器 -->
            <div class="home-indicator">
                <div class="home-indicator-bar"></div>
            </div>
        </div>
    </div>
    
    <script>
        function refreshQR() {
            const btn = document.querySelector('.refresh-btn');
            btn.style.transform = 'rotate(360deg)';

            setTimeout(() => {
                btn.style.transform = 'rotate(0deg)';
                showToast('二维码已刷新', 'success');
            }, 500);
        }

        function saveQR() {
            showToast('二维码图片已保存到相册', 'success');
        }

        function copyLink() {
            // 模拟复制链接
            const link = 'https://mahjongcode.app/join/MJ20240118001';

            if (navigator.clipboard) {
                navigator.clipboard.writeText(link).then(() => {
                    showToast('链接已复制到剪贴板', 'success');
                });
            } else {
                showToast('链接已复制: ' + link, 'success');
            }
        }

        function shareToWechat() {
            showToast('正在打开微信分享...', 'normal');
        }

        function shareToQQ() {
            showToast('正在打开QQ分享...', 'normal');
        }

        function shareToWeibo() {
            showToast('正在打开微博分享...', 'normal');
        }

        async function shareMore() {
            const options = ['短信', '邮件', '钉钉', 'Telegram'];
            const choice = await showSelect(
                '更多分享方式',
                '选择分享平台：',
                options
            );

            if (choice !== null) {
                showToast(`正在打开${options[choice]}分享...`, 'normal');
            }
        }
        
        async function setExpiry() {
            const options = ['1小时', '2小时', '6小时', '12小时', '24小时', '永久'];
            const choice = await showSelect(
                '设置有效期',
                '选择二维码有效期：',
                options
            );

            if (choice !== null) {
                showToast(`有效期已设置为: ${options[choice]}`, 'success');
            }
        }

        async function setMaxPlayers() {
            const maxPlayers = await showPrompt(
                '设置最大人数',
                '请输入最大人数 (2-8人)：',
                {
                    placeholder: '4',
                    defaultValue: '4',
                    inputType: 'number'
                }
            );

            if (maxPlayers && maxPlayers >= 2 && maxPlayers <= 8) {
                showToast(`最大人数已设置为: ${maxPlayers}人`, 'success');
            } else if (maxPlayers) {
                await showAlert('输入错误', '人数必须在2-8人之间');
            }
        }

        async function setPermission() {
            const permissions = ['公开', '仅好友', '需要验证'];
            const choice = await showSelect(
                '设置加入权限',
                '选择加入权限：',
                permissions
            );

            if (choice !== null) {
                showToast(`加入权限已设置为: ${permissions[choice]}`, 'success');
            }
        }

        async function showSettings() {
            const options = ['修改牌局名称', '设置密码保护', '高级设置'];
            const choice = await showSelect(
                '更多设置',
                '选择设置选项：',
                options
            );

            if (choice !== null) {
                showToast(`正在${options[choice]}...`, 'normal');
            }
        }
        
        // 模拟实时更新统计数据
        setInterval(() => {
            const scanCount = document.querySelector('.stat-value');
            if (scanCount && Math.random() > 0.95) {
                const current = parseInt(scanCount.textContent);
                scanCount.textContent = current + 1;
            }
        }, 5000);
    </script>
</body>
</html>
