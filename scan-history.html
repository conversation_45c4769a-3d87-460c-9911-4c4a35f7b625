<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>麻将码 - 扫描历史</title>
    <link rel="stylesheet" href="styles/ios-base.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="js/modal.js"></script>
    <style>
        .history-header {
            background: linear-gradient(135deg, #2E7D32 0%, #4CAF50 100%);
            color: white;
            padding: 24px 16px;
            text-align: center;
        }
        
        .header-title {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 8px;
        }
        
        .header-subtitle {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .search-section {
            padding: 16px;
            background: white;
            border-bottom: 1px solid var(--border-color);
        }
        
        .search-input {
            width: 100%;
            padding: 12px 16px 12px 40px;
            border: 1px solid var(--border-color);
            border-radius: 20px;
            font-size: 16px;
            background: var(--background-color);
            position: relative;
        }
        
        .search-icon {
            position: absolute;
            left: 28px;
            top: 28px;
            color: var(--text-secondary);
            font-size: 16px;
        }
        
        .filter-chips {
            display: flex;
            gap: 8px;
            padding: 16px;
            overflow-x: auto;
            background: white;
            border-bottom: 1px solid var(--border-color);
        }
        
        .filter-chip {
            padding: 8px 16px;
            border: 1px solid var(--border-color);
            border-radius: 20px;
            background: white;
            color: var(--text-secondary);
            font-size: 14px;
            white-space: nowrap;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .filter-chip.active {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }
        
        .history-list {
            padding: 16px;
        }
        
        .date-group {
            margin-bottom: 24px;
        }
        
        .date-header {
            font-size: 14px;
            font-weight: 600;
            color: var(--text-secondary);
            margin-bottom: 12px;
            padding: 0 8px;
        }
        
        .scan-item {
            background: white;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            gap: 16px;
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .scan-item:hover {
            transform: translateY(-1px);
        }
        
        .scan-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
        }
        
        .icon-success {
            background: var(--ios-green);
        }
        
        .icon-failed {
            background: var(--ios-red);
        }
        
        .icon-manual {
            background: var(--ios-blue);
        }
        
        .scan-info {
            flex: 1;
        }
        
        .scan-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 4px;
        }
        
        .scan-meta {
            font-size: 12px;
            color: var(--text-secondary);
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .scan-result {
            text-align: right;
        }
        
        .result-status {
            font-size: 12px;
            font-weight: 500;
            padding: 4px 8px;
            border-radius: 6px;
            margin-bottom: 4px;
        }
        
        .status-success {
            background: #E8F5E8;
            color: var(--ios-green);
        }
        
        .status-failed {
            background: #FFEBEE;
            color: var(--ios-red);
        }
        
        .status-manual {
            background: #E3F2FD;
            color: var(--ios-blue);
        }
        
        .result-time {
            font-size: 12px;
            color: var(--text-secondary);
        }
        
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: var(--text-secondary);
        }
        
        .empty-icon {
            font-size: 48px;
            margin-bottom: 16px;
            opacity: 0.5;
        }
        
        .empty-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 8px;
        }
        
        .empty-subtitle {
            font-size: 14px;
            line-height: 1.4;
        }
        
        .clear-history-btn {
            position: fixed;
            bottom: 100px;
            right: 20px;
            width: 56px;
            height: 56px;
            background: var(--ios-red);
            border-radius: 28px;
            border: none;
            color: white;
            font-size: 20px;
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(244, 67, 54, 0.3);
            transition: all 0.2s;
            z-index: 10;
        }
        
        .clear-history-btn:hover {
            transform: scale(1.1);
        }
        
        .qr-preview {
            width: 40px;
            height: 40px;
            background: var(--background-color);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            color: var(--text-secondary);
            margin-left: 8px;
        }
    </style>
</head>
<body>
    <div class="device-container">
        <div class="screen">
            <!-- 状态栏 -->
            <div class="status-bar">
                <div class="status-left">
                    <span>9:41</span>
                </div>
                <div class="status-right">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <div class="battery">
                        <div class="battery-fill"></div>
                    </div>
                </div>
            </div>
            
            <!-- 导航栏 -->
            <div class="nav-bar">
                <a href="scan.html" class="nav-button">
                    <i class="fas fa-arrow-left"></i>
                </a>
                <div class="nav-title">扫描历史</div>
                <div class="nav-button" style="opacity: 0; pointer-events: none;">
                    <!-- 占位元素，保持布局平衡 -->
                </div>
            </div>
            
            <!-- 安全区域内容 -->
            <div class="safe-area">
                <!-- 头部信息 -->
                <div class="history-header">
                    <div class="header-title">扫描历史记录</div>
                    <div class="header-subtitle">共扫描 23 次，成功 20 次</div>
                </div>
                
                <!-- 搜索栏 -->
                <div class="search-section">
                    <div style="position: relative;">
                        <i class="fas fa-search search-icon"></i>
                        <input type="text" class="search-input" placeholder="搜索牌局名称或内容">
                    </div>
                </div>
                
                <!-- 筛选标签 -->
                <div class="filter-chips">
                    <div class="filter-chip active" onclick="setFilter('all')">全部</div>
                    <div class="filter-chip" onclick="setFilter('success')">成功</div>
                    <div class="filter-chip" onclick="setFilter('failed')">失败</div>
                    <div class="filter-chip" onclick="setFilter('manual')">手动输入</div>
                    <div class="filter-chip" onclick="setFilter('today')">今天</div>
                    <div class="filter-chip" onclick="setFilter('week')">本周</div>
                </div>
                
                <!-- 历史记录列表 -->
                <div class="history-list">
                    <!-- 今天 -->
                    <div class="date-group">
                        <div class="date-header">今天</div>
                        
                        <div class="scan-item" onclick="viewScanDetail('scan1')">
                            <div class="scan-icon icon-success">
                                <i class="fas fa-qrcode"></i>
                            </div>
                            <div class="scan-info">
                                <div class="scan-title">周末家庭局</div>
                                <div class="scan-meta">
                                    <span><i class="fas fa-camera"></i> 相机扫描</span>
                                    <span><i class="fas fa-users"></i> 4人局</span>
                                </div>
                            </div>
                            <div class="scan-result">
                                <div class="result-status status-success">扫描成功</div>
                                <div class="result-time">14:32</div>
                            </div>
                            <div class="qr-preview">
                                <i class="fas fa-qrcode"></i>
                            </div>
                        </div>
                        
                        <div class="scan-item" onclick="viewScanDetail('scan2')">
                            <div class="scan-icon icon-failed">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="scan-info">
                                <div class="scan-title">无效二维码</div>
                                <div class="scan-meta">
                                    <span><i class="fas fa-camera"></i> 相机扫描</span>
                                    <span><i class="fas fa-times"></i> 识别失败</span>
                                </div>
                            </div>
                            <div class="scan-result">
                                <div class="result-status status-failed">扫描失败</div>
                                <div class="result-time">12:15</div>
                            </div>
                            <div class="qr-preview">
                                <i class="fas fa-question"></i>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 昨天 -->
                    <div class="date-group">
                        <div class="date-header">昨天</div>
                        
                        <div class="scan-item" onclick="viewScanDetail('scan3')">
                            <div class="scan-icon icon-success">
                                <i class="fas fa-qrcode"></i>
                            </div>
                            <div class="scan-info">
                                <div class="scan-title">朋友聚会</div>
                                <div class="scan-meta">
                                    <span><i class="fas fa-image"></i> 相册识别</span>
                                    <span><i class="fas fa-users"></i> 4人局</span>
                                </div>
                            </div>
                            <div class="scan-result">
                                <div class="result-status status-success">扫描成功</div>
                                <div class="result-time">19:45</div>
                            </div>
                            <div class="qr-preview">
                                <i class="fas fa-qrcode"></i>
                            </div>
                        </div>
                        
                        <div class="scan-item" onclick="viewScanDetail('scan4')">
                            <div class="scan-icon icon-manual">
                                <i class="fas fa-keyboard"></i>
                            </div>
                            <div class="scan-info">
                                <div class="scan-title">MJ20240117002</div>
                                <div class="scan-meta">
                                    <span><i class="fas fa-keyboard"></i> 手动输入</span>
                                    <span><i class="fas fa-check"></i> 验证成功</span>
                                </div>
                            </div>
                            <div class="scan-result">
                                <div class="result-status status-manual">手动输入</div>
                                <div class="result-time">15:20</div>
                            </div>
                            <div class="qr-preview">
                                <i class="fas fa-keyboard"></i>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 本周 -->
                    <div class="date-group">
                        <div class="date-header">本周</div>
                        
                        <div class="scan-item" onclick="viewScanDetail('scan5')">
                            <div class="scan-icon icon-success">
                                <i class="fas fa-qrcode"></i>
                            </div>
                            <div class="scan-info">
                                <div class="scan-title">麻将馆对局</div>
                                <div class="scan-meta">
                                    <span><i class="fas fa-camera"></i> 相机扫描</span>
                                    <span><i class="fas fa-map-marker-alt"></i> 金牌麻将馆</span>
                                </div>
                            </div>
                            <div class="scan-result">
                                <div class="result-status status-success">扫描成功</div>
                                <div class="result-time">周三 15:00</div>
                            </div>
                            <div class="qr-preview">
                                <i class="fas fa-qrcode"></i>
                            </div>
                        </div>
                        
                        <div class="scan-item" onclick="viewScanDetail('scan6')">
                            <div class="scan-icon icon-failed">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="scan-info">
                                <div class="scan-title">光线不足</div>
                                <div class="scan-meta">
                                    <span><i class="fas fa-camera"></i> 相机扫描</span>
                                    <span><i class="fas fa-lightbulb"></i> 建议开启手电筒</span>
                                </div>
                            </div>
                            <div class="scan-result">
                                <div class="result-status status-failed">扫描失败</div>
                                <div class="result-time">周二 20:30</div>
                            </div>
                            <div class="qr-preview">
                                <i class="fas fa-question"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 清除历史按钮 -->
            <button class="clear-history-btn" onclick="clearHistory()">
                <i class="fas fa-trash"></i>
            </button>
            
            <!-- Home指示器 -->
            <div class="home-indicator">
                <div class="home-indicator-bar"></div>
            </div>
        </div>
    </div>
    
    <script>
        function setFilter(type) {
            // 移除所有active类
            document.querySelectorAll('.filter-chip').forEach(chip => {
                chip.classList.remove('active');
            });
            
            // 添加active类到当前标签
            event.target.classList.add('active');
            
            // 这里可以添加筛选逻辑
            console.log('筛选类型:', type);
        }
        
        async function viewScanDetail(scanId) {
            // 根据scanId显示详细信息
            await showAlert('扫描详情', `扫描ID: ${scanId}\n时间: 2024-01-18 14:32\n结果: 成功\n牌局: 周末家庭局`);
        }

        // 导出功能已移除

        async function clearHistory() {
            const result = await showConfirm(
                '清除历史记录',
                '确认清除所有扫描历史记录？\n此操作不可恢复。',
                {
                    confirmText: '确认清除',
                    cancelText: '取消',
                    confirmClass: 'danger'
                }
            );

            if (result) {
                showToast('历史记录已清除', 'success');
                // 这里可以添加清除逻辑
            }
        }
    </script>
</body>
</html>
