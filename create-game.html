<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>麻将码 - 创建牌局</title>
    <link rel="stylesheet" href="styles/ios-base.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="js/modal.js"></script>
    <script src="js/gameData.js"></script>
    <script src="js/friendsManager.js"></script>
    <style>
        .form-section {
            padding: 24px 16px;
        }
        
        .form-group {
            margin-bottom: 24px;
        }
        
        .form-label {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 8px;
            display: block;
        }
        
        .form-input {
            width: 100%;
            padding: 16px;
            border: 2px solid var(--border-color);
            border-radius: 12px;
            font-size: 16px;
            background: white;
            transition: border-color 0.2s;
            box-sizing: border-box;
        }
        
        .form-input:focus {
            outline: none;
            border-color: var(--primary-color);
        }
        
        .form-select {
            width: 100%;
            padding: 16px;
            border: 2px solid var(--border-color);
            border-radius: 12px;
            font-size: 16px;
            background: white;
            cursor: pointer;
            appearance: none;
            background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><polyline points="6,9 12,15 18,9"></polyline></svg>');
            background-repeat: no-repeat;
            background-position: right 16px center;
            background-size: 20px;
        }
        
        .player-list {
            background: white;
            border-radius: 12px;
            padding: 16px;
            margin-top: 8px;
        }
        
        .player-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 0;
            border-bottom: 1px solid var(--border-color);
        }
        
        .player-item:last-child {
            border-bottom: none;
        }
        
        .player-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .player-avatar {
            width: 40px;
            height: 40px;
            border-radius: 20px;
            background: var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
        }
        
        .player-name {
            font-size: 16px;
            font-weight: 500;
            color: var(--text-primary);
        }

        .player-name-input {
            border: none;
            background: none;
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
            padding: 0;
            margin: 0;
            width: 100%;
            outline: none;
        }

        .player-name-input:focus {
            background: rgba(76, 175, 80, 0.1);
            border-radius: 4px;
            padding: 2px 4px;
        }
        
        .player-role {
            font-size: 12px;
            color: var(--text-secondary);
        }
        
        .add-player-section {
            display: flex;
            justify-content: center;
            margin-top: 12px;
        }

        .add-player-btn {
            padding: 16px 24px;
            border: 2px dashed var(--border-color);
            border-radius: 12px;
            background: transparent;
            color: var(--text-secondary);
            font-size: 16px;
            cursor: pointer;
            transition: all 0.2s;
            min-width: 150px;
        }

        .add-player-btn:hover {
            border-color: var(--primary-color);
            color: var(--primary-color);
            background: rgba(76, 175, 80, 0.05);
        }


        
        .game-rules {
            background: white;
            border-radius: 12px;
            padding: 16px;
            margin-top: 8px;
        }
        
        .rule-option {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 0;
            border-bottom: 1px solid var(--border-color);
        }
        
        .rule-option:last-child {
            border-bottom: none;
        }
        
        .rule-label {
            font-size: 16px;
            color: var(--text-primary);
        }
        
        .rule-toggle {
            width: 50px;
            height: 30px;
            background: var(--ios-gray);
            border-radius: 15px;
            position: relative;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        
        .rule-toggle.active {
            background: var(--primary-color);
        }
        
        .rule-toggle::after {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 26px;
            height: 26px;
            background: white;
            border-radius: 13px;
            transition: transform 0.2s;
        }
        
        .rule-toggle.active::after {
            transform: translateX(20px);
        }
        
        .qr-preview {
            background: white;
            border-radius: 12px;
            padding: 24px;
            text-align: center;
            margin-top: 8px;
        }
        
        .qr-code {
            width: 200px;
            height: 200px;
            background: #f0f0f0;
            border-radius: 12px;
            margin: 0 auto 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 48px;
            color: var(--text-secondary);
        }
        
        .qr-info {
            font-size: 14px;
            color: var(--text-secondary);
            margin-bottom: 16px;
        }
        
        .action-buttons {
            display: flex;
            gap: 12px;
            padding: 16px;
            background: white;
            border-top: 1px solid var(--border-color);
        }
        
        .btn-outline {
            flex: 1;
            padding: 16px;
            border: 2px solid var(--primary-color);
            border-radius: 12px;
            background: transparent;
            color: var(--primary-color);
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .btn-outline:hover {
            background: var(--primary-color);
            color: white;
        }
        
        .location-input {
            position: relative;
        }
        
        .location-btn {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: var(--primary-color);
            font-size: 18px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div class="device-container">
        <div class="screen">
            <!-- 状态栏 -->
            <div class="status-bar">
                <div class="status-left">
                    <span>9:41</span>
                </div>
                <div class="status-right">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <div class="battery">
                        <div class="battery-fill"></div>
                    </div>
                </div>
            </div>
            
            <!-- 导航栏 -->
            <div class="nav-bar">
                <a href="index.html" class="nav-button">
                    <i class="fas fa-times"></i>
                </a>
                <div class="nav-title">创建牌局</div>
                <button class="nav-button" onclick="createGame()">完成</button>
            </div>
            
            <!-- 安全区域内容 -->
            <div class="safe-area">
                <div class="form-section">
                    <!-- 基本信息 -->
                    <div class="form-group">
                        <label class="form-label">牌局名称</label>
                        <input type="text" id="gameName" class="form-input" placeholder="输入牌局名称" value="周末家庭局">
                    </div>

                    <div class="form-group">
                        <label class="form-label">麻将类型</label>
                        <select id="gameType" class="form-select">
                            <option value="guangdong">广东麻将</option>
                            <option value="sichuan">四川麻将</option>
                            <option value="guobiao">国标麻将</option>
                            <option value="beijing">北京麻将</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label class="form-label">游戏地点</label>
                        <div class="location-input">
                            <input type="text" id="gameLocation" class="form-input" placeholder="输入或选择地点" value="家里客厅">
                            <button class="location-btn" onclick="selectLocation()">
                                <i class="fas fa-map-marker-alt"></i>
                            </button>
                        </div>
                    </div>
                    
                    <!-- 参与玩家 -->
                    <div class="form-group">
                        <label class="form-label">参与玩家 (4人)</label>
                        <div class="player-list" id="playerList">
                            <div class="player-item" data-player-id="p1">
                                <div class="player-info">
                                    <div class="player-avatar">我</div>
                                    <div>
                                        <input type="text" class="player-name-input" value="张三" placeholder="玩家姓名" style="border: none; background: none; font-size: 16px; font-weight: 600;">
                                        <div class="player-role">房主</div>
                                    </div>
                                </div>
                                <i class="fas fa-crown" style="color: var(--accent-color);"></i>
                            </div>

                            <div class="player-item" data-player-id="p2">
                                <div class="player-info">
                                    <div class="player-avatar">李</div>
                                    <div>
                                        <input type="text" class="player-name-input" value="李四" placeholder="玩家姓名" style="border: none; background: none; font-size: 16px; font-weight: 600;">
                                        <div class="player-role">玩家</div>
                                    </div>
                                </div>
                                <button onclick="removePlayer('p2')" style="background: none; border: none; color: var(--ios-red);">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>

                            <div class="player-item" data-player-id="p3">
                                <div class="player-info">
                                    <div class="player-avatar">王</div>
                                    <div>
                                        <input type="text" class="player-name-input" value="王五" placeholder="玩家姓名" style="border: none; background: none; font-size: 16px; font-weight: 600;">
                                        <div class="player-role">玩家</div>
                                    </div>
                                </div>
                                <button onclick="removePlayer('p3')" style="background: none; border: none; color: var(--ios-red);">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>

                            <div class="player-item" data-player-id="p4">
                                <div class="player-info">
                                    <div class="player-avatar">赵</div>
                                    <div>
                                        <input type="text" class="player-name-input" value="赵六" placeholder="玩家姓名" style="border: none; background: none; font-size: 16px; font-weight: 600;">
                                        <div class="player-role">玩家</div>
                                    </div>
                                </div>
                                <button onclick="removePlayer('p4')" style="background: none; border: none; color: var(--ios-red);">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                        <div class="add-player-section">
                            <button class="add-player-btn" id="addPlayerBtn" onclick="addPlayerDirectly()">
                                <i class="fas fa-plus"></i> 添加玩家
                            </button>
                        </div>
                    </div>
                    
                    <!-- 游戏规则 -->
                    <div class="form-group">
                        <label class="form-label">游戏规则</label>
                        <div class="game-rules">
                            <div class="rule-option">
                                <span class="rule-label">可胡七对</span>
                                <div class="rule-toggle active" onclick="toggleRule(this)"></div>
                            </div>
                            <div class="rule-option">
                                <span class="rule-label">可杠上花</span>
                                <div class="rule-toggle active" onclick="toggleRule(this)"></div>
                            </div>
                            <div class="rule-option">
                                <span class="rule-label">可抢杠胡</span>
                                <div class="rule-toggle" onclick="toggleRule(this)"></div>
                            </div>
                            <div class="rule-option">
                                <span class="rule-label">飘分规则</span>
                                <div class="rule-toggle" onclick="toggleRule(this)"></div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 二维码预览 -->
                    <div class="form-group">
                        <label class="form-label">牌局二维码</label>
                        <div class="qr-preview">
                            <div class="qr-code">
                                <i class="fas fa-qrcode"></i>
                            </div>
                            <div class="qr-info">
                                扫描此二维码可快速加入牌局<br>
                                牌局ID: MJ20240118001
                            </div>
                            <button class="btn-secondary" onclick="shareQR()">
                                <i class="fas fa-share"></i> 分享二维码
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- 操作按钮 -->
                <div class="action-buttons">
                    <button class="btn-outline" onclick="saveTemplate()">保存模板</button>
                    <button class="btn-primary" onclick="startGame()">开始游戏</button>
                </div>
            </div>
            
            <!-- Home指示器 -->
            <div class="home-indicator">
                <div class="home-indicator-bar"></div>
            </div>
        </div>
    </div>
    
    <script>
        function toggleRule(element) {
            element.classList.toggle('active');
        }
        
        async function selectLocation() {
            const locations = [
                '家里客厅',
                '家里书房',
                '朋友家',
                '麻将馆',
                '棋牌室',
                '茶楼',
                '会所',
                '公司休息室',
                '社区活动中心',
                '自定义地点...'
            ];

            const choice = await showSelect(
                '选择地点',
                '请选择游戏地点：',
                locations
            );

            if (choice !== null) {
                if (choice === locations.length - 1) {
                    // 选择了"自定义地点"
                    const customLocation = await showPrompt(
                        '自定义地点',
                        '请输入游戏地点：',
                        {
                            placeholder: '输入地点名称',
                            confirmText: '确定',
                            cancelText: '取消'
                        }
                    );

                    if (customLocation && customLocation.trim()) {
                        document.getElementById('gameLocation').value = customLocation.trim();
                        showToast(`已设置地点：${customLocation.trim()}`, 'success');
                    }
                } else {
                    document.getElementById('gameLocation').value = locations[choice];
                    showToast(`已选择地点：${locations[choice]}`, 'success');
                }
            }
        }

        // 直接添加玩家
        async function addPlayerDirectly() {
            await addPlayerManually();
        }

        // 手动添加玩家
        async function addPlayerManually() {
            // 检查当前玩家数量
            const currentPlayers = document.querySelectorAll('.player-item').length;
            if (currentPlayers >= 8) {
                await showAlert('提示', '最多支持8名玩家');
                return;
            }

            const playerName = await showPrompt(
                '添加玩家',
                '请输入玩家昵称：',
                {
                    placeholder: '输入昵称',
                    confirmText: '添加',
                    cancelText: '取消'
                }
            );

            if (playerName && playerName.trim()) {
                const trimmedName = playerName.trim();

                // 检查是否重名
                const existingNames = Array.from(document.querySelectorAll('.player-name-input'))
                    .map(input => input.value.trim())
                    .filter(name => name);

                if (existingNames.includes(trimmedName)) {
                    await showAlert('提示', '玩家昵称不能重复');
                    return;
                }

                // 创建新玩家项
                const newPlayerId = 'p' + (Date.now() % 10000);

                const newPlayerHTML = `
                    <div class="player-item" data-player-id="${newPlayerId}">
                        <div class="player-info">
                            <div class="player-avatar">🀄</div>
                            <div>
                                <input type="text" class="player-name-input" value="${trimmedName}" placeholder="玩家姓名" style="border: none; background: none; font-size: 16px; font-weight: 600;">
                                <div class="player-role">玩家</div>
                            </div>
                        </div>
                        <button onclick="removePlayer('${newPlayerId}')" style="background: none; border: none; color: var(--ios-red);">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                `;

                // 添加到玩家列表
                const playerListContainer = document.querySelector('.player-list');

                // 在玩家列表容器内的最后位置插入新玩家
                playerListContainer.insertAdjacentHTML('beforeend', newPlayerHTML);

                // 新添加的玩家使用固定麻将头像，无需事件监听

                // 更新玩家数量显示
                updatePlayerCount();

                // 检查是否需要隐藏添加按钮
                updateAddPlayerButton();

                showToast(`已添加玩家：${trimmedName}`, 'success');
            }
        }

        async function shareQR() {
            const options = ['微信', 'QQ', '微博', '复制链接'];
            const choice = await showSelect(
                '分享二维码',
                '选择分享方式：',
                options
            );

            if (choice !== null) {
                showToast(`正在分享到${options[choice]}...`, 'success');
            }
        }

        async function saveTemplate() {
            showToast('模板已保存', 'success');
        }

        async function startGame() {
            // 收集表单数据
            const gameData = collectGameData();

            // 验证数据
            const validation = validateGameData(gameData);
            if (!validation.valid) {
                await showAlert('输入错误', validation.message);
                return;
            }

            const result = await showConfirm(
                '创建牌局',
                `确认创建牌局"${gameData.name}"并开始游戏？\n\n参与玩家：${gameData.players.map(p => p.name).join('、')}`,
                {
                    confirmText: '开始游戏',
                    cancelText: '取消'
                }
            );

            if (result) {
                try {
                    showToast('正在创建牌局...', 'success');

                    // 创建新游戏
                    const newGameId = await createNewGame(gameData);

                    if (newGameId) {
                        showToast('牌局创建成功！', 'success');
                        setTimeout(() => {
                            window.location.href = 'game-detail.html';
                        }, 1000);
                    } else {
                        await showAlert('创建失败', '无法创建牌局，请重试');
                    }
                } catch (error) {
                    console.error('创建牌局失败:', error);
                    await showAlert('创建失败', '创建过程中出现错误：' + error.message);
                }
            }
        }

        // 收集表单数据
        function collectGameData() {
            const gameName = document.getElementById('gameName').value.trim();
            const gameType = document.getElementById('gameType').value;
            const gameLocation = document.getElementById('gameLocation').value.trim();

            // 获取用户信息
            const userInfo = getUserInfo();

            // 收集玩家信息
            const players = [];
            const playerItems = document.querySelectorAll('.player-item');

            playerItems.forEach((item, index) => {
                const nameInput = item.querySelector('.player-name-input');
                const avatar = item.querySelector('.player-avatar').textContent;
                const isOwner = item.querySelector('.fas.fa-crown') !== null;

                if (nameInput && nameInput.value.trim()) {
                    players.push({
                        id: item.dataset.playerId || `p${index + 1}`,
                        name: isOwner ? userInfo.name : nameInput.value.trim(), // 房主使用个人资料的用户名
                        avatar: isOwner ? userInfo.avatar : avatar, // 房主使用个人资料的头像
                        totalScore: 0,
                        isOwner: isOwner
                    });
                }
            });

            // 收集游戏规则
            const rules = {};
            const ruleToggles = document.querySelectorAll('.rule-toggle');
            const ruleLabels = ['canQiDui', 'canGangShangHua', 'canQiangGangHu', 'piaoFenRule'];

            ruleToggles.forEach((toggle, index) => {
                rules[ruleLabels[index]] = toggle.classList.contains('active');
            });

            return {
                name: gameName,
                type: getGameTypeName(gameType),
                location: gameLocation,
                players: players,
                rules: rules
            };
        }

        // 验证游戏数据
        function validateGameData(gameData) {
            if (!gameData.name) {
                return { valid: false, message: '请输入牌局名称' };
            }

            if (gameData.name.length > 20) {
                return { valid: false, message: '牌局名称不能超过20个字符' };
            }

            if (!gameData.location) {
                return { valid: false, message: '请输入游戏地点' };
            }

            if (gameData.players.length < 2) {
                return { valid: false, message: '至少需要2名玩家' };
            }

            if (gameData.players.length > 4) {
                return { valid: false, message: '最多支持4名玩家' };
            }

            // 检查玩家姓名是否重复
            const playerNames = gameData.players.map(p => p.name);
            const uniqueNames = [...new Set(playerNames)];
            if (playerNames.length !== uniqueNames.length) {
                return { valid: false, message: '玩家姓名不能重复' };
            }

            return { valid: true };
        }

        // 获取游戏类型名称
        function getGameTypeName(typeValue) {
            const typeMap = {
                'guangdong': '广东麻将',
                'sichuan': '四川麻将',
                'guobiao': '国标麻将',
                'beijing': '北京麻将'
            };
            return typeMap[typeValue] || '广东麻将';
        }

        // 创建新游戏
        async function createNewGame(gameData) {
            try {
                // 生成新的游戏ID
                const gameId = 'game_' + Date.now();

                // 构造游戏数据
                const newGame = {
                    id: gameId,
                    name: gameData.name,
                    type: gameData.type,
                    location: gameData.location,
                    startTime: new Date().toISOString(),
                    status: 'ongoing',
                    players: gameData.players,
                    rounds: [],
                    rules: gameData.rules,
                    qrCode: `MJ${Date.now().toString().slice(-8)}`
                };

                // 获取现有数据
                const allData = gameDataManager.getGameData() || { games: {}, currentGameId: null };

                // 添加新游戏
                allData.games[gameId] = newGame;
                allData.currentGameId = gameId;

                // 保存数据
                gameDataManager.saveGameData(allData);

                console.log('新游戏创建成功:', newGame);
                return gameId;
            } catch (error) {
                console.error('创建游戏失败:', error);
                return null;
            }
        }

        // 移除玩家
        async function removePlayer(playerId) {
            const playerItem = document.querySelector(`[data-player-id="${playerId}"]`);
            if (!playerItem) return;

            const playerName = playerItem.querySelector('.player-name-input').value.trim();
            const isOwner = playerItem.querySelector('.fas.fa-crown') !== null;

            // 如果是房主，不允许删除
            if (isOwner) {
                await showAlert('提示', '房主不能被移除');
                return;
            }

            // 确认删除
            const result = await showConfirm(
                '移除玩家',
                `确认移除玩家"${playerName}"？`,
                {
                    confirmText: '移除',
                    cancelText: '取消',
                    confirmClass: 'danger'
                }
            );

            if (result) {
                playerItem.remove();
                updatePlayerCount();
                updateAddPlayerButton();
                showToast(`已移除玩家：${playerName}`, 'success');
            }
        }

        // 更新玩家数量显示
        function updatePlayerCount() {
            const playerItems = document.querySelectorAll('.player-item');
            const label = document.querySelector('.form-label');
            if (label && label.textContent.includes('参与玩家')) {
                label.textContent = `参与玩家 (${playerItems.length}人)`;
            }
        }

        // 更新添加玩家按钮的显示状态
        function updateAddPlayerButton() {
            const playerItems = document.querySelectorAll('.player-item');
            const addButton = document.getElementById('addPlayerBtn');

            if (addButton) {
                if (playerItems.length >= 8) {
                    addButton.style.display = 'none';
                } else {
                    addButton.style.display = 'block';
                    addButton.innerHTML = `<i class="fas fa-plus"></i> 添加玩家 (${playerItems.length}/8)`;
                }
            }
        }

        // 更新头像显示
        function updatePlayerAvatar(input) {
            const playerItem = input.closest('.player-item');
            const avatar = playerItem.querySelector('.player-avatar');
            const name = input.value.trim();

            if (name) {
                avatar.textContent = name.charAt(0);
            }
        }

        // 获取用户信息
        function getUserInfo() {
            const saved = localStorage.getItem('userProfile');
            if (saved) {
                const userInfo = JSON.parse(saved);
                userInfo.avatar = '🀄'; // 确保头像是麻将图标
                return userInfo;
            }

            // 默认用户信息
            return {
                name: '麻将玩家',
                avatar: '🀄'
            };
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 获取用户信息并设置房主信息
            const userInfo = getUserInfo();
            const ownerNameInput = document.querySelector('[data-player-id="p1"] .player-name-input');
            const ownerAvatar = document.querySelector('[data-player-id="p1"] .player-avatar');

            if (ownerNameInput) {
                ownerNameInput.value = userInfo.name;
            }
            if (ownerAvatar) {
                ownerAvatar.textContent = userInfo.avatar;
            }

            // 为玩家姓名输入框添加事件监听
            document.querySelectorAll('.player-name-input').forEach(input => {
                input.addEventListener('input', function() {
                    updatePlayerAvatar(this);
                });
            });

            // 初始化玩家数量显示和添加按钮状态
            updatePlayerCount();
            updateAddPlayerButton();

            // 生成随机牌局ID显示
            const qrInfo = document.querySelector('.qr-info');
            if (qrInfo) {
                const randomId = 'MJ' + Date.now().toString().slice(-8);
                qrInfo.innerHTML = `扫描此二维码可快速加入牌局<br>牌局ID: ${randomId}`;
            }
        });

        function createGame() {
            startGame();
        }
    </script>
</body>
</html>
