<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>麻将码 - 首页</title>
    <link rel="stylesheet" href="styles/ios-base.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="js/modal.js"></script>
    <script src="js/gameData.js"></script>
    <style>
        /* 首页特定样式 */
        .hero-section {
            background: linear-gradient(135deg, #2E7D32 0%, #4CAF50 100%);
            color: white;
            padding: 32px 16px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .hero-section::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><text y="50" font-size="30" fill="rgba(255,255,255,0.1)">🀄</text></svg>') repeat;
            background-size: 80px 80px;
            animation: float 30s linear infinite;
        }
        
        .hero-content {
            position: relative;
            z-index: 2;
        }
        
        .app-logo {
            width: 80px;
            height: 80px;
            background: rgba(255,255,255,0.2);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 16px;
            font-size: 40px;
            backdrop-filter: blur(10px);
        }
        
        .quick-actions {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
            padding: 24px 16px;
        }
        
        .action-card {
            background: white;
            border-radius: 16px;
            padding: 24px 16px;
            text-align: center;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            transition: transform 0.2s, box-shadow 0.2s;
            cursor: pointer;
            text-decoration: none;
            color: inherit;
        }
        
        .action-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.15);
        }
        
        .action-icon {
            width: 48px;
            height: 48px;
            background: var(--primary-color);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 12px;
            color: white;
            font-size: 24px;
        }
        
        .action-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 4px;
        }
        
        .action-subtitle {
            font-size: 12px;
            color: var(--text-secondary);
        }
        
        .recent-games {
            padding: 0 16px 24px;
        }
        
        .section-title {
            font-size: 20px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 16px;
            padding: 0 8px;
        }
        
        .game-item {
            background: white;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .game-info {
            flex: 1;
        }
        
        .game-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 4px;
        }
        
        .game-meta {
            font-size: 14px;
            color: var(--text-secondary);
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .game-location {
            font-size: 12px;
            color: var(--text-secondary);
            margin-top: 4px;
        }

        .game-location i {
            margin-right: 4px;
        }
        
        .game-status {
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-completed {
            background: #E8F5E8;
            color: var(--ios-green);
        }
        
        .status-ongoing {
            background: #FFF3E0;
            color: var(--ios-orange);
        }
        
        .bottom-nav {
            background: white;
            border-top: 1px solid var(--border-color);
            padding: 8px 0;
            display: flex;
            justify-content: space-around;
            position: sticky;
            bottom: 0;
        }
        
        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 8px;
            text-decoration: none;
            color: var(--ios-gray);
            transition: color 0.2s;
        }
        
        .nav-item.active {
            color: var(--primary-color);
        }
        
        .nav-item i {
            font-size: 24px;
            margin-bottom: 4px;
        }
        
        .nav-item span {
            font-size: 10px;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="device-container">
        <div class="screen">
            <!-- 状态栏 -->
            <div class="status-bar">
                <div class="status-left">
                    <span>9:41</span>
                </div>
                <div class="status-right">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <div class="battery">
                        <div class="battery-fill"></div>
                    </div>
                </div>
            </div>
            
            <!-- 导航栏 -->
            <div class="nav-bar">
                <button class="nav-button">
                    <i class="fas fa-user-circle"></i>
                </button>
                <div class="nav-title">麻将码</div>
                <button class="nav-button">
                    <i class="fas fa-bell"></i>
                </button>
            </div>
            
            <!-- 安全区域内容 -->
            <div class="safe-area">
                <!-- 英雄区域 -->
                <div class="hero-section">
                    <div class="hero-content">
                        <div class="app-logo">🀄</div>
                        <h1 style="margin: 0 0 8px; font-size: 28px; font-weight: 700;">麻将码</h1>
                        <p style="margin: 0; font-size: 16px; opacity: 0.9;">扫码记录，智能统计</p>
                    </div>
                </div>
                
                <!-- 快捷操作 -->
                <div class="quick-actions">
                    <a href="scan.html" class="action-card">
                        <div class="action-icon">
                            <i class="fas fa-qrcode"></i>
                        </div>
                        <div class="action-title">扫码加入</div>
                        <div class="action-subtitle">扫描二维码快速加入牌局</div>
                    </a>
                    
                    <a href="create-game.html" class="action-card">
                        <div class="action-icon">
                            <i class="fas fa-plus"></i>
                        </div>
                        <div class="action-title">创建牌局</div>
                        <div class="action-subtitle">开始新的麻将记录</div>
                    </a>
                    
                    <a href="stats.html" class="action-card">
                        <div class="action-icon">
                            <i class="fas fa-chart-bar"></i>
                        </div>
                        <div class="action-title">战绩统计</div>
                        <div class="action-subtitle">查看详细数据分析</div>
                    </a>
                    
                    <a href="history.html" class="action-card">
                        <div class="action-icon">
                            <i class="fas fa-history"></i>
                        </div>
                        <div class="action-title">历史记录</div>
                        <div class="action-subtitle">浏览过往牌局</div>
                    </a>
                </div>
                
                <!-- 最近牌局 -->
                <div class="recent-games">
                    <h2 class="section-title">最近牌局</h2>

                    <div id="recentGamesList">
                        <!-- 最近牌局将通过JavaScript动态生成 -->
                    </div>
                </div>
                
                <!-- 底部导航 -->
                <div class="bottom-nav">
                    <a href="index.html" class="nav-item active">
                        <i class="fas fa-home"></i>
                        <span>首页</span>
                    </a>
                    <a href="scan.html" class="nav-item">
                        <i class="fas fa-qrcode"></i>
                        <span>扫码</span>
                    </a>
                    <a href="stats.html" class="nav-item">
                        <i class="fas fa-chart-line"></i>
                        <span>统计</span>
                    </a>
                    <a href="profile.html" class="nav-item">
                        <i class="fas fa-user"></i>
                        <span>我的</span>
                    </a>
                </div>
            </div>
            
            <!-- Home指示器 -->
            <div class="home-indicator">
                <div class="home-indicator-bar"></div>
            </div>
        </div>
    </div>
    <script>
        // 页面加载时初始化数据
        document.addEventListener('DOMContentLoaded', function() {
            console.log('=== 首页初始化 ===');
            loadRecentGames();

            // 监听数据更新事件
            window.addEventListener('gameDataUpdated', function(event) {
                console.log('首页检测到游戏数据更新，重新加载数据', event.detail);
                loadRecentGames();
            });

            // 监听额外的数据变化事件
            window.addEventListener('mahjongDataChanged', function(event) {
                console.log('首页检测到麻将数据变化，重新加载数据', event.detail);
                loadRecentGames();
            });

            // 监听localStorage变化事件
            window.addEventListener('storage', function(event) {
                if (event.key === 'mahjongGameData') {
                    console.log('首页检测到localStorage变化，重新加载数据');
                    setTimeout(() => {
                        loadRecentGames();
                    }, 50);
                }
            });

            // 监听页面可见性变化，确保数据同步
            document.addEventListener('visibilitychange', function() {
                if (!document.hidden) {
                    console.log('首页重新可见，刷新数据');
                    setTimeout(() => {
                        loadRecentGames();
                    }, 100);
                }
            });

            // 监听窗口焦点变化
            window.addEventListener('focus', function() {
                console.log('首页重新获得焦点，刷新数据');
                setTimeout(() => {
                    loadRecentGames();
                }, 100);
            });

            // 定期检查数据变化（作为备用方案）
            let lastDataHash = '';
            setInterval(() => {
                try {
                    const currentData = gameDataManager.getGameData();
                    const currentHash = JSON.stringify(currentData);
                    if (currentHash !== lastDataHash) {
                        console.log('首页定期检查发现数据变化，刷新显示');
                        lastDataHash = currentHash;
                        loadRecentGames();
                    }
                } catch (error) {
                    console.error('定期检查数据时发生错误:', error);
                }
            }, 2000); // 每2秒检查一次
        });

        // 加载最近牌局
        function loadRecentGames() {
            console.log('=== 加载最近牌局 ===');
            const gameData = gameDataManager.getGameData();
            console.log('游戏数据:', gameData);

            if (!gameData || !gameData.games) {
                console.log('没有游戏数据，显示空状态');
                showEmptyState();
                return;
            }

            const recentGamesList = document.getElementById('recentGamesList');
            if (!recentGamesList) return;

            const games = Object.values(gameData.games);

            if (games.length === 0) {
                showEmptyState();
                return;
            }

            // 按创建时间排序，最新的在前面
            games.sort((a, b) => new Date(b.startTime) - new Date(a.startTime));

            // 显示最近的游戏
            recentGamesList.innerHTML = games.map(game => {
                const startTime = new Date(game.startTime);
                const timeText = formatGameTime(startTime);
                const statusClass = game.status === 'ongoing' ? 'status-ongoing' : 'status-completed';
                const statusText = game.status === 'ongoing' ? '进行中' : '已结束';

                // 计算游戏统计
                const totalRounds = game.rounds.length;
                const duration = game.status === 'completed' && game.endTime ?
                    calculateDuration(game.startTime, game.endTime) :
                    calculateDuration(game.startTime, new Date().toISOString());

                return `
                    <div class="game-item" onclick="openGame('${game.id}')">
                        <div class="game-info">
                            <div class="game-title">${game.name}</div>
                            <div class="game-meta">
                                <span><i class="fas fa-calendar"></i> ${timeText}</span>
                                <span><i class="fas fa-users"></i> ${game.players.length}人</span>
                                ${totalRounds > 0 ? `<span><i class="fas fa-list"></i> ${totalRounds}局</span>` : ''}
                                <span><i class="fas fa-clock"></i> ${duration}</span>
                            </div>
                            <div class="game-location">
                                <i class="fas fa-map-marker-alt"></i> ${game.location || '未设置地点'}
                            </div>
                        </div>
                        <div class="game-status ${statusClass}">${statusText}</div>
                    </div>
                `;
            }).join('');
        }

        // 显示空状态
        function showEmptyState() {
            const recentGamesList = document.getElementById('recentGamesList');
            if (!recentGamesList) return;

            recentGamesList.innerHTML = `
                <div style="text-align: center; padding: 40px 20px; color: var(--text-secondary);">
                    <i class="fas fa-gamepad" style="font-size: 48px; opacity: 0.3; margin-bottom: 16px;"></i>
                    <div style="font-size: 16px; margin-bottom: 8px;">还没有牌局记录</div>
                    <div style="font-size: 14px; margin-bottom: 20px;">点击"创建牌局"开始第一局游戏</div>
                    <button onclick="window.location.href='create-game.html'" style="
                        background: var(--primary-color);
                        color: white;
                        border: none;
                        border-radius: 8px;
                        padding: 12px 24px;
                        font-size: 14px;
                        cursor: pointer;
                    ">
                        <i class="fas fa-plus"></i> 创建牌局
                    </button>
                </div>
            `;
        }

        // 计算游戏时长
        function calculateDuration(startTime, endTime) {
            const start = new Date(startTime);
            const end = new Date(endTime);
            const diffMs = end - start;
            const hours = Math.floor(diffMs / (1000 * 60 * 60));
            const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));

            if (hours > 0) {
                return `${hours}h${minutes > 0 ? ` ${minutes}m` : ''}`;
            } else {
                return `${minutes}m`;
            }
        }

        // 格式化游戏时间
        function formatGameTime(date) {
            const now = new Date();
            const diffMs = now - date;
            const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

            if (diffDays === 0) {
                return '今天 ' + date.toLocaleTimeString('zh-CN', {hour: '2-digit', minute: '2-digit'});
            } else if (diffDays === 1) {
                return '昨天 ' + date.toLocaleTimeString('zh-CN', {hour: '2-digit', minute: '2-digit'});
            } else if (diffDays < 7) {
                return `${diffDays}天前`;
            } else {
                return date.toLocaleDateString('zh-CN');
            }
        }

        // 打开游戏
        function openGame(gameId) {
            gameDataManager.setCurrentGame(gameId);
            window.location.href = 'game-detail.html';
        }
    </script>
</body>
</html>
