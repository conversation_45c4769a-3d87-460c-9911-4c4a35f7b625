<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分数输入测试</title>
    <link rel="stylesheet" href="styles/ios-base.css">
    <style>
        .score-input-card {
            background: white;
            border-radius: 16px;
            padding: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            margin: 16px;
            max-width: 300px;
        }
        
        .score-input-card:hover {
            box-shadow: 0 4px 16px rgba(0,0,0,0.15);
            transform: translateY(-2px);
        }
        
        .player-info {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 12px;
        }
        
        .player-avatar-small {
            width: 36px;
            height: 36px;
            border-radius: 18px;
            background: var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }
        
        .player-name-small {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
        }
        
        .score-input {
            width: 100%;
            height: 48px;
            padding: 12px 16px;
            border: 2px solid var(--border-color);
            border-radius: 12px;
            font-size: 18px;
            font-weight: 600;
            text-align: center;
            background: var(--background-color);
            transition: border-color 0.2s;
            box-sizing: border-box;
        }
        
        .quick-scores {
            margin-top: 10px;
        }
        
        .quick-scores-section {
            display: flex;
            flex-direction: column;
            gap: 6px;
        }
        
        .quick-scores-row {
            display: flex;
            gap: 6px;
            justify-content: center;
        }
        
        .quick-scores-label {
            font-size: 11px;
            color: var(--text-secondary);
            text-align: center;
            margin-bottom: 2px;
            font-weight: 500;
        }
        
        .quick-score-btn {
            flex: 1;
            height: 36px;
            padding: 8px;
            border: 1.5px solid;
            border-radius: 10px;
            font-size: 13px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            min-width: 44px;
            text-align: center;
            box-sizing: border-box;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .quick-score-btn.positive {
            background: linear-gradient(135deg, #E8F5E8 0%, #C8E6C9 100%);
            border-color: #4CAF50;
            color: #2E7D32;
        }
        
        .quick-score-btn.positive:hover {
            background: linear-gradient(135deg, #4CAF50 0%, #66BB6A 100%);
            color: white;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
        }
        
        .quick-score-btn.negative {
            background: linear-gradient(135deg, #FFEBEE 0%, #FFCDD2 100%);
            border-color: #F44336;
            color: #C62828;
        }
        
        .quick-score-btn.negative:hover {
            background: linear-gradient(135deg, #F44336 0%, #EF5350 100%);
            color: white;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(244, 67, 54, 0.3);
        }
        
        .clear-score-btn {
            width: 100%;
            height: 32px;
            padding: 6px;
            margin-top: 8px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            background: #F5F5F5;
            color: var(--text-secondary);
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s;
            box-sizing: border-box;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .clear-score-btn:hover {
            background: var(--ios-gray);
            color: white;
            border-color: var(--ios-gray);
        }
    </style>
</head>
<body>
    <div style="padding: 20px; background: var(--background-color); min-height: 100vh;">
        <h2>优化后的分数输入区域</h2>
        
        <div class="score-input-card">
            <div class="player-info">
                <div class="player-avatar-small">麻</div>
                <div class="player-name-small">麻将玩家</div>
            </div>
            <input type="number" class="score-input" placeholder="0" id="score-test">
            <div class="quick-scores">
                <div class="quick-scores-section">
                    <div class="quick-scores-label">常用分数</div>
                    <div class="quick-scores-row">
                        <button class="quick-score-btn positive" onclick="setScore(80)">+80</button>
                        <button class="quick-score-btn positive" onclick="setScore(40)">+40</button>
                    </div>
                    <div class="quick-scores-row">
                        <button class="quick-score-btn negative" onclick="setScore(-20)">-20</button>
                        <button class="quick-score-btn negative" onclick="setScore(-30)">-30</button>
                    </div>
                    <button class="clear-score-btn" onclick="clearScore()">清除</button>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function setScore(score) {
            document.getElementById('score-test').value = score;
        }
        
        function clearScore() {
            document.getElementById('score-test').value = '';
        }
    </script>
</body>
</html>
