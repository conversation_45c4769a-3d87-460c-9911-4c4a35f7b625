// 头像素材库管理
class AvatarLibrary {
    constructor() {
        this.avatars = this.initAvatarLibrary();
    }

    // 初始化头像库
    initAvatarLibrary() {
        return {
            // 动物系列
            animals: [
                { id: 'panda', emoji: '🐼', name: '熊猫', category: 'animals' },
                { id: 'cat', emoji: '🐱', name: '小猫', category: 'animals' },
                { id: 'dog', emoji: '🐶', name: '小狗', category: 'animals' },
                { id: 'rabbit', emoji: '🐰', name: '兔子', category: 'animals' },
                { id: 'fox', emoji: '🦊', name: '狐狸', category: 'animals' },
                { id: 'bear', emoji: '🐻', name: '熊', category: 'animals' },
                { id: 'tiger', emoji: '🐯', name: '老虎', category: 'animals' },
                { id: 'lion', emoji: '🦁', name: '狮子', category: 'animals' },
                { id: 'monkey', emoji: '🐵', name: '猴子', category: 'animals' },
                { id: 'pig', emoji: '🐷', name: '小猪', category: 'animals' }
            ],
            
            // 表情系列
            expressions: [
                { id: 'smile', emoji: '😊', name: '微笑', category: 'expressions' },
                { id: 'happy', emoji: '😄', name: '开心', category: 'expressions' },
                { id: 'cool', emoji: '😎', name: '酷', category: 'expressions' },
                { id: 'wink', emoji: '😉', name: '眨眼', category: 'expressions' },
                { id: 'laugh', emoji: '😂', name: '大笑', category: 'expressions' },
                { id: 'love', emoji: '😍', name: '爱心眼', category: 'expressions' },
                { id: 'thinking', emoji: '🤔', name: '思考', category: 'expressions' },
                { id: 'star_eyes', emoji: '🤩', name: '星星眼', category: 'expressions' },
                { id: 'peace', emoji: '😌', name: '安详', category: 'expressions' },
                { id: 'party', emoji: '🥳', name: '派对', category: 'expressions' }
            ],

            // 职业系列
            professions: [
                { id: 'student', emoji: '👨‍🎓', name: '学生', category: 'professions' },
                { id: 'teacher', emoji: '👨‍🏫', name: '老师', category: 'professions' },
                { id: 'doctor', emoji: '👨‍⚕️', name: '医生', category: 'professions' },
                { id: 'chef', emoji: '👨‍🍳', name: '厨师', category: 'professions' },
                { id: 'artist', emoji: '👨‍🎨', name: '艺术家', category: 'professions' },
                { id: 'scientist', emoji: '👨‍🔬', name: '科学家', category: 'professions' },
                { id: 'programmer', emoji: '👨‍💻', name: '程序员', category: 'professions' },
                { id: 'businessman', emoji: '👨‍💼', name: '商务', category: 'professions' },
                { id: 'farmer', emoji: '👨‍🌾', name: '农民', category: 'professions' },
                { id: 'mechanic', emoji: '👨‍🔧', name: '技工', category: 'professions' }
            ],

            // 运动系列
            sports: [
                { id: 'soccer', emoji: '⚽', name: '足球', category: 'sports' },
                { id: 'basketball', emoji: '🏀', name: '篮球', category: 'sports' },
                { id: 'tennis', emoji: '🎾', name: '网球', category: 'sports' },
                { id: 'swimming', emoji: '🏊‍♂️', name: '游泳', category: 'sports' },
                { id: 'cycling', emoji: '🚴‍♂️', name: '骑行', category: 'sports' },
                { id: 'running', emoji: '🏃‍♂️', name: '跑步', category: 'sports' },
                { id: 'golf', emoji: '🏌️‍♂️', name: '高尔夫', category: 'sports' },
                { id: 'skiing', emoji: '⛷️', name: '滑雪', category: 'sports' },
                { id: 'surfing', emoji: '🏄‍♂️', name: '冲浪', category: 'sports' },
                { id: 'climbing', emoji: '🧗‍♂️', name: '攀岩', category: 'sports' }
            ],

            // 食物系列
            foods: [
                { id: 'pizza', emoji: '🍕', name: '披萨', category: 'foods' },
                { id: 'burger', emoji: '🍔', name: '汉堡', category: 'foods' },
                { id: 'sushi', emoji: '🍣', name: '寿司', category: 'foods' },
                { id: 'noodles', emoji: '🍜', name: '面条', category: 'foods' },
                { id: 'coffee', emoji: '☕', name: '咖啡', category: 'foods' },
                { id: 'cake', emoji: '🎂', name: '蛋糕', category: 'foods' },
                { id: 'ice_cream', emoji: '🍦', name: '冰淇淋', category: 'foods' },
                { id: 'donut', emoji: '🍩', name: '甜甜圈', category: 'foods' },
                { id: 'apple', emoji: '🍎', name: '苹果', category: 'foods' },
                { id: 'avocado', emoji: '🥑', name: '牛油果', category: 'foods' }
            ],

            // 自然系列
            nature: [
                { id: 'sun', emoji: '☀️', name: '太阳', category: 'nature' },
                { id: 'moon', emoji: '🌙', name: '月亮', category: 'nature' },
                { id: 'star', emoji: '⭐', name: '星星', category: 'nature' },
                { id: 'rainbow', emoji: '🌈', name: '彩虹', category: 'nature' },
                { id: 'flower', emoji: '🌸', name: '花朵', category: 'nature' },
                { id: 'tree', emoji: '🌳', name: '大树', category: 'nature' },
                { id: 'mountain', emoji: '🏔️', name: '雪山', category: 'nature' },
                { id: 'ocean', emoji: '🌊', name: '海浪', category: 'nature' },
                { id: 'fire', emoji: '🔥', name: '火焰', category: 'nature' },
                { id: 'lightning', emoji: '⚡', name: '闪电', category: 'nature' }
            ]
        };
    }

    // 获取所有头像
    getAllAvatars() {
        const allAvatars = [];
        Object.values(this.avatars).forEach(category => {
            allAvatars.push(...category);
        });
        return allAvatars;
    }

    // 按分类获取头像
    getAvatarsByCategory(category) {
        return this.avatars[category] || [];
    }

    // 获取分类列表
    getCategories() {
        return [
            { key: 'animals', name: '动物', icon: '🐼' },
            { key: 'expressions', name: '表情', icon: '😊' },
            { key: 'professions', name: '职业', icon: '👨‍💻' },
            { key: 'sports', name: '运动', icon: '⚽' },
            { key: 'foods', name: '美食', icon: '🍕' },
            { key: 'nature', name: '自然', icon: '🌸' }
        ];
    }

    // 根据ID获取头像
    getAvatarById(id) {
        const allAvatars = this.getAllAvatars();
        return allAvatars.find(avatar => avatar.id === id);
    }

    // 搜索头像
    searchAvatars(keyword) {
        const allAvatars = this.getAllAvatars();
        if (!keyword) return allAvatars;

        return allAvatars.filter(avatar => 
            avatar.name.toLowerCase().includes(keyword.toLowerCase()) ||
            avatar.id.toLowerCase().includes(keyword.toLowerCase())
        );
    }

    // 获取随机头像
    getRandomAvatar() {
        const allAvatars = this.getAllAvatars();
        const randomIndex = Math.floor(Math.random() * allAvatars.length);
        return allAvatars[randomIndex];
    }

    // 获取推荐头像（基于用户名）
    getRecommendedAvatars(userName) {
        if (!userName) return this.getAllAvatars().slice(0, 12);

        const firstChar = userName.charAt(0).toLowerCase();
        const allAvatars = this.getAllAvatars();
        
        // 根据用户名首字母推荐相关头像
        const recommended = allAvatars.filter(avatar => {
            return avatar.name.charAt(0).toLowerCase() === firstChar ||
                   avatar.id.charAt(0).toLowerCase() === firstChar;
        });

        // 如果推荐的不够，补充一些热门头像
        if (recommended.length < 12) {
            const popular = [
                'panda', 'cat', 'smile', 'cool', 'pizza', 'sun'
            ];
            
            popular.forEach(id => {
                const avatar = this.getAvatarById(id);
                if (avatar && !recommended.find(r => r.id === id)) {
                    recommended.push(avatar);
                }
            });
        }

        return recommended.slice(0, 12);
    }

    // 获取最近使用的头像
    getRecentAvatars() {
        const recent = JSON.parse(localStorage.getItem('recentAvatars') || '[]');
        return recent.map(id => this.getAvatarById(id)).filter(Boolean);
    }

    // 添加到最近使用
    addToRecent(avatarId) {
        let recent = JSON.parse(localStorage.getItem('recentAvatars') || '[]');
        
        // 移除已存在的
        recent = recent.filter(id => id !== avatarId);
        
        // 添加到开头
        recent.unshift(avatarId);
        
        // 只保留最近10个
        recent = recent.slice(0, 10);
        
        localStorage.setItem('recentAvatars', JSON.stringify(recent));
    }

    // 获取默认头像
    getDefaultAvatar() {
        return this.getAvatarById('smile') || this.getAllAvatars()[0];
    }
}

// 创建全局实例
const avatarLibrary = new AvatarLibrary();

// 导出到全局
window.avatarLibrary = avatarLibrary;
