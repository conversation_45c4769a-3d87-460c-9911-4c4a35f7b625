# 麻将二维码App需求文档

## 1. 项目概述

### 1.1 项目名称
麻将码 (MahjongCode)

### 1.2 项目描述
一款专为中国麻将爱好者设计的移动应用，通过二维码技术实现麻将牌局的快速记录、分享和统计分析。用户可以扫描或生成二维码来记录牌局信息，与朋友分享战绩，并查看详细的游戏统计数据。

## 2. 用户细分

### 2.1 主要用户群体

#### 2.1.1 休闲麻将玩家 (60%)
- **特征**: 偶尔与朋友家人打麻将，注重娱乐性
- **需求**: 简单记录输赢，与朋友分享有趣时刻
- **使用场景**: 家庭聚会、朋友聚餐后的麻将局

#### 2.1.2 麻将爱好者 (30%)
- **特征**: 经常打麻将，对战绩有一定关注
- **需求**: 详细的战绩统计，技巧提升建议
- **使用场景**: 麻将馆、棋牌室的定期聚会

#### 2.1.3 专业麻将玩家 (10%)
- **特征**: 参与比赛，对数据分析要求高
- **需求**: 专业级统计分析，对手信息记录
- **使用场景**: 麻将比赛、高水平对局

### 2.2 次要用户群体
- 麻将馆经营者：需要管理客户信息和场次记录
- 麻将教练：需要分析学员表现和进步情况

## 3. 核心功能

### 3.1 二维码生成与扫描
- **功能描述**: 为每局麻将生成唯一二维码，包含局次信息
- **核心价值**: 快速记录和分享牌局信息
- **具体功能**:
  - 生成包含时间、地点、参与者的二维码
  - 扫描二维码快速加入牌局记录
  - 支持批量生成多局二维码

### 3.2 牌局记录管理
- **功能描述**: 详细记录每局麻将的各项数据
- **核心价值**: 完整的游戏历史追踪
- **具体功能**:
  - 记录参与者姓名/昵称
  - 记录每局得分和排名
  - 记录游戏时长和地点
  - 支持多种麻将规则（国标、广东、四川等）
  - 添加局次备注和照片

### 3.3 战绩统计分析
- **功能描述**: 多维度分析用户麻将表现
- **核心价值**: 帮助用户了解自己的游戏水平和进步空间
- **具体功能**:
  - 胜率统计（总体、月度、年度）
  - 平均得分和排名分析
  - 对手胜负记录
  - 游戏习惯分析（常玩时间、地点等）
  - 进步趋势图表展示

### 3.4 社交分享功能
- **功能描述**: 与朋友分享麻将相关内容
- **核心价值**: 增强社交互动，提升用户粘性
- **具体功能**:
  - 分享精彩局次到社交媒体
  - 邀请朋友加入牌局记录
  - 好友战绩对比
  - 麻将群组创建和管理

## 4. 非功能需求

### 4.1 性能需求
- 应用启动时间 < 3秒
- 二维码生成/扫描响应时间 < 1秒
- 数据同步延迟 < 5秒
- 支持离线模式基本功能

### 4.2 可用性需求
- 界面简洁直观，符合中国用户使用习惯
- 支持中文语音输入和识别
- 适配iOS和Android主流设备
- 支持横竖屏切换

### 4.3 安全性需求
- 用户数据本地加密存储
- 二维码信息加密传输
- 支持指纹/面部识别登录
- 数据备份和恢复机制

### 4.4 兼容性需求
- iOS 12.0+ / Android 8.0+
- 支持iPhone 6及以上设备
- 适配各种屏幕尺寸
- 支持深色模式

## 5. 数据模型

### 5.1 用户信息 (User)
```
- userId: 用户唯一标识
- nickname: 昵称
- avatar: 头像
- phone: 手机号
- createTime: 注册时间
- lastLoginTime: 最后登录时间
- preferences: 用户偏好设置
```

### 5.2 牌局信息 (Game)
```
- gameId: 牌局唯一标识
- qrCode: 二维码内容
- gameType: 麻将类型（国标/广东/四川等）
- startTime: 开始时间
- endTime: 结束时间
- location: 地点
- rounds: 总局数
- status: 状态（进行中/已结束）
- creatorId: 创建者ID
- participants: 参与者列表
```

### 5.3 局次记录 (Round)
```
- roundId: 局次唯一标识
- gameId: 所属牌局ID
- roundNumber: 局次编号
- scores: 各玩家得分
- winner: 胡牌者
- roundType: 局次类型（自摸/点炮等）
- duration: 局次时长
- timestamp: 时间戳
```

### 5.4 玩家统计 (PlayerStats)
```
- playerId: 玩家ID
- totalGames: 总局数
- totalRounds: 总牌局数
- winRate: 胜率
- avgScore: 平均得分
- bestScore: 最高得分
- totalWins: 总胜局
- monthlyStats: 月度统计
- yearlyStats: 年度统计
```

### 5.5 好友关系 (Friendship)
```
- friendshipId: 关系ID
- userId: 用户ID
- friendId: 好友ID
- status: 关系状态
- createTime: 建立时间
- gameHistory: 对战历史
```

## 6. 技术架构

### 6.1 前端技术
- 框架: React Native / Flutter
- UI组件: 原生iOS设计规范
- 状态管理: Redux / MobX
- 本地存储: SQLite

### 6.2 后端技术
- 数据库: SQLite (本地存储)
- 数据同步: 本地文件系统
- 二维码: QR Code生成库
- 图片处理: 本地图片库

### 6.3 第三方服务
- 地图服务: 高德地图API
- 图片素材: Unsplash API
- 图标库: FontAwesome
- 分享SDK: 微信/QQ分享

## 7. 开发计划

### 7.1 第一阶段 (MVP)
- 基础二维码生成和扫描
- 简单牌局记录
- 基础统计功能
- 核心UI界面

### 7.2 第二阶段
- 完善统计分析功能
- 社交分享功能
- 好友系统
- 数据导入导出

### 7.3 第三阶段
- 高级分析功能
- 个性化推荐
- 社区功能
- 性能优化

## 8. 成功指标

### 8.1 用户指标
- 日活跃用户数 > 1000
- 用户留存率 > 60% (7天)
- 平均会话时长 > 5分钟

### 8.2 功能指标
- 二维码扫描成功率 > 95%
- 数据记录完成率 > 80%
- 用户满意度 > 4.5/5.0

### 8.3 技术指标
- 应用崩溃率 < 1%
- 响应时间 < 2秒
- 数据准确率 > 99%
