// 自定义弹窗组件
class CustomModal {
    constructor() {
        this.currentModal = null;
        this.currentToast = null;
    }

    // 显示确认对话框
    confirm(title, message, options = {}) {
        return new Promise((resolve) => {
            const {
                confirmText = '确定',
                cancelText = '取消',
                confirmClass = 'primary',
                cancelClass = 'secondary'
            } = options;

            const modalHtml = `
                <div class="modal-overlay" id="customModal">
                    <div class="modal-content">
                        <div class="modal-title">${title}</div>
                        <div class="modal-message">${message}</div>
                        <div class="modal-buttons">
                            <button class="modal-button ${cancelClass}" onclick="customModal.close(false)">
                                ${cancelText}
                            </button>
                            <button class="modal-button ${confirmClass}" onclick="customModal.close(true)">
                                ${confirmText}
                            </button>
                        </div>
                    </div>
                </div>
            `;

            this.showModal(modalHtml, resolve);
        });
    }

    // 显示提示对话框
    alert(title, message, options = {}) {
        return new Promise((resolve) => {
            const {
                confirmText = '确定',
                confirmClass = 'primary'
            } = options;

            const modalHtml = `
                <div class="modal-overlay" id="customModal">
                    <div class="modal-content">
                        <div class="modal-title">${title}</div>
                        <div class="modal-message">${message}</div>
                        <div class="modal-buttons">
                            <button class="modal-button ${confirmClass}" onclick="customModal.close(true)">
                                ${confirmText}
                            </button>
                        </div>
                    </div>
                </div>
            `;

            this.showModal(modalHtml, resolve);
        });
    }

    // 显示输入对话框
    prompt(title, message, options = {}) {
        return new Promise((resolve) => {
            const {
                placeholder = '',
                defaultValue = '',
                confirmText = '确定',
                cancelText = '取消',
                inputType = 'text'
            } = options;

            const modalHtml = `
                <div class="modal-overlay" id="customModal">
                    <div class="modal-content">
                        <div class="modal-title">${title}</div>
                        <div class="modal-message">${message}</div>
                        <input type="${inputType}" class="modal-input" id="modalInput" 
                               placeholder="${placeholder}" value="${defaultValue}">
                        <div class="modal-buttons">
                            <button class="modal-button secondary" onclick="customModal.close(null)">
                                ${cancelText}
                            </button>
                            <button class="modal-button primary" onclick="customModal.closeWithInput()">
                                ${confirmText}
                            </button>
                        </div>
                    </div>
                </div>
            `;

            this.showModal(modalHtml, resolve);
            
            // 聚焦到输入框
            setTimeout(() => {
                const input = document.getElementById('modalInput');
                if (input) {
                    input.focus();
                    input.select();
                }
            }, 100);
        });
    }

    // 显示选择对话框
    select(title, message, options = []) {
        return new Promise((resolve) => {
            const optionsHtml = options.map((option, index) => 
                `<button class="modal-button secondary" style="margin-bottom: 8px;" 
                         onclick="customModal.close(${index})">${option}</button>`
            ).join('');

            const modalHtml = `
                <div class="modal-overlay" id="customModal">
                    <div class="modal-content">
                        <div class="modal-title">${title}</div>
                        <div class="modal-message">${message}</div>
                        <div style="display: flex; flex-direction: column; gap: 8px;">
                            ${optionsHtml}
                            <button class="modal-button secondary" onclick="customModal.close(null)">
                                取消
                            </button>
                        </div>
                    </div>
                </div>
            `;

            this.showModal(modalHtml, resolve);
        });
    }

    // 显示Toast提示
    toast(message, type = 'normal', duration = 3000) {
        // 移除现有的toast
        if (this.currentToast) {
            this.currentToast.remove();
        }

        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        toast.textContent = message;
        
        document.body.appendChild(toast);
        this.currentToast = toast;

        // 显示动画
        setTimeout(() => {
            toast.classList.add('show');
        }, 10);

        // 自动隐藏
        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
                if (this.currentToast === toast) {
                    this.currentToast = null;
                }
            }, 300);
        }, duration);
    }

    // 显示模态框
    showModal(html, resolve) {
        // 移除现有的模态框
        if (this.currentModal) {
            this.currentModal.remove();
        }

        // 创建新的模态框
        const modalContainer = document.createElement('div');
        modalContainer.innerHTML = html;
        const modal = modalContainer.firstElementChild;
        
        document.body.appendChild(modal);
        this.currentModal = modal;
        this.currentResolve = resolve;

        // 显示动画
        setTimeout(() => {
            modal.classList.add('show');
        }, 10);

        // 点击背景关闭
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                this.close(null);
            }
        });

        // ESC键关闭
        const handleEsc = (e) => {
            if (e.key === 'Escape') {
                this.close(null);
                document.removeEventListener('keydown', handleEsc);
            }
        };
        document.addEventListener('keydown', handleEsc);
    }

    // 关闭模态框
    close(result) {
        if (this.currentModal) {
            this.currentModal.classList.remove('show');
            setTimeout(() => {
                if (this.currentModal && this.currentModal.parentNode) {
                    this.currentModal.parentNode.removeChild(this.currentModal);
                }
                this.currentModal = null;
            }, 300);
        }

        if (this.currentResolve) {
            this.currentResolve(result);
            this.currentResolve = null;
        }
    }

    // 关闭并返回输入值
    closeWithInput() {
        const input = document.getElementById('modalInput');
        const value = input ? input.value.trim() : '';
        this.close(value || null);
    }
}

// 创建全局实例
const customModal = new CustomModal();

// 兼容性函数，替换原生弹窗
window.showAlert = (title, message, options) => customModal.alert(title, message, options);
window.showConfirm = (title, message, options) => customModal.confirm(title, message, options);
window.showPrompt = (title, message, options) => customModal.prompt(title, message, options);
window.showSelect = (title, message, options) => customModal.select(title, message, options);
window.showToast = (message, type, duration) => customModal.toast(message, type, duration);

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CustomModal;
}
