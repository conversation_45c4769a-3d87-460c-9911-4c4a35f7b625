<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>麻将码 - 战绩统计</title>
    <link rel="stylesheet" href="styles/ios-base.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .stats-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, #34C759 100%);
            color: white;
            padding: 32px 20px;
            text-align: center;
            position: relative;
            overflow: hidden;
            border-radius: 0 0 24px 24px;
            margin-bottom: 20px;
        }

        .stats-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1.5" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="1" fill="rgba(255,255,255,0.1)"/></svg>');
            opacity: 0.3;
        }
        
        .stats-header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><text y="50" font-size="20" fill="rgba(255,255,255,0.1)">📊</text></svg>') repeat;
            background-size: 50px 50px;
            animation: float 20s linear infinite;
        }
        
        .stats-content {
            position: relative;
            z-index: 2;
        }
        
        .stats-title {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 8px;
        }
        
        .stats-subtitle {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .time-filter {
            display: flex;
            background: rgba(255,255,255,0.2);
            border-radius: 20px;
            padding: 4px;
            margin-top: 16px;
        }
        
        .filter-btn {
            flex: 1;
            padding: 10px 16px;
            border: none;
            background: transparent;
            color: white;
            border-radius: 16px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .filter-btn:hover:not(.active) {
            background: rgba(255, 255, 255, 0.1);
        }

        .filter-btn.active {
            background: white;
            color: var(--primary-color);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        }
        
        .overview-cards {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
            padding: 0 20px 24px;
            margin-top: -12px;
        }

        .overview-card {
            background: white;
            border-radius: 20px;
            padding: 24px 16px;
            text-align: center;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
            border: 1px solid rgba(0,0,0,0.05);
        }

        .overview-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.12);
        }
        
        .card-icon {
            width: 56px;
            height: 56px;
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 16px;
            font-size: 28px;
            color: white;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        
        .icon-games {
            background: linear-gradient(135deg, var(--primary-color), #5856D6);
        }

        .icon-winrate {
            background: linear-gradient(135deg, #FF9500, #FF6B35);
        }

        .icon-score {
            background: linear-gradient(135deg, #34C759, #30D158);
        }

        .icon-time {
            background: linear-gradient(135deg, #FF3B30, #FF6B6B);
        }
        
        .card-value {
            font-size: 28px;
            font-weight: 800;
            color: var(--text-primary);
            margin-bottom: 6px;
            line-height: 1.2;
        }
        
        .card-label {
            font-size: 12px;
            color: var(--text-secondary);
        }
        
        .chart-section {
            padding: 0 20px 24px;
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .section-title {
            font-size: 20px;
            font-weight: 700;
            color: var(--text-primary);
        }

        .view-all-btn {
            color: var(--primary-color);
            font-size: 14px;
            font-weight: 600;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 20px;
            background: rgba(0, 122, 255, 0.1);
            transition: all 0.2s ease;
        }

        .view-all-btn:hover {
            background: rgba(0, 122, 255, 0.2);
        }

        .chart-card {
            background: white;
            border-radius: 20px;
            padding: 24px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            margin-bottom: 20px;
            border: 1px solid rgba(0,0,0,0.05);
        }
        
        .chart-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 16px;
        }
        
        .chart-placeholder {
            height: 220px;
            background: linear-gradient(135deg, rgba(0, 122, 255, 0.05) 0%, rgba(52, 199, 89, 0.05) 100%);
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--primary-color);
            font-size: 64px;
            margin-bottom: 20px;
            border: 2px dashed rgba(0, 122, 255, 0.2);
            position: relative;
        }

        .chart-placeholder::after {
            content: '图表数据加载中...';
            position: absolute;
            bottom: 20px;
            font-size: 14px;
            color: var(--text-secondary);
        }

        .chart-legend {
            display: flex;
            justify-content: space-around;
            font-size: 13px;
            background: rgba(0, 122, 255, 0.05);
            padding: 16px;
            border-radius: 12px;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 500;
        }
        
        .legend-color {
            width: 12px;
            height: 12px;
            border-radius: 6px;
        }
        
        .color-win {
            background: var(--ios-green);
        }
        
        .color-lose {
            background: var(--ios-red);
        }
        
        .color-draw {
            background: var(--ios-gray);
        }
        
        .recent-games {
            padding: 0 20px 24px;
        }

        .game-item {
            background: white;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            display: flex;
            align-items: center;
            justify-content: space-between;
            border: 1px solid rgba(0,0,0,0.05);
            transition: all 0.3s ease;
        }

        .game-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 25px rgba(0,0,0,0.12);
        }
        
        .game-info {
            flex: 1;
        }
        
        .game-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 4px;
        }
        
        .game-meta {
            font-size: 14px;
            color: var(--text-secondary);
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .game-result {
            text-align: right;
        }
        
        .result-score {
            font-size: 18px;
            font-weight: 700;
            margin-bottom: 4px;
        }
        
        .result-score.positive {
            color: var(--ios-green);
        }
        
        .result-score.negative {
            color: var(--ios-red);
        }
        
        .result-rank {
            font-size: 12px;
            color: var(--text-secondary);
        }
        
        .bottom-nav {
            background: white;
            border-top: 1px solid var(--border-color);
            padding: 8px 0;
            display: flex;
            justify-content: space-around;
            position: sticky;
            bottom: 0;
            box-shadow: 0 -2px 8px rgba(0,0,0,0.1);
            max-width: 400px;
            margin: 0 auto;
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 12px 16px;
            text-decoration: none;
            color: var(--ios-gray);
            transition: all 0.2s;
            border-radius: 12px;
            min-width: 60px;
        }

        .nav-item.active {
            color: var(--primary-color);
            background: rgba(46, 125, 50, 0.1);
        }

        .nav-item:hover {
            background: rgba(0, 0, 0, 0.05);
        }
        
        .nav-item i {
            font-size: 24px;
            margin-bottom: 4px;
        }
        
        .nav-item span {
            font-size: 10px;
            font-weight: 500;
        }
    </style>
    <script src="js/modal.js"></script>
    <script src="js/gameData.js"></script>
</head>
<body>
    <div class="device-container">
        <div class="screen">
            <!-- 状态栏 -->
            <div class="status-bar">
                <div class="status-left">
                    <span>9:41</span>
                </div>
                <div class="status-right">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <div class="battery">
                        <div class="battery-fill"></div>
                    </div>
                </div>
            </div>
            
            <!-- 导航栏 -->
            <div class="nav-bar">
                <a href="index.html" class="nav-button">
                    <i class="fas fa-arrow-left"></i>
                </a>
                <div class="nav-title">战绩统计</div>
                <div class="nav-button" style="opacity: 0; pointer-events: none;">
                    <!-- 占位元素，保持布局平衡 -->
                </div>
            </div>
            
            <!-- 安全区域内容 -->
            <div class="safe-area">
                <!-- 统计头部 -->
                <div class="stats-header">
                    <div class="stats-content">
                        <div class="stats-title">我的战绩</div>
                        <div class="stats-subtitle">总览你的麻将表现</div>
                        
                        <div class="time-filter">
                            <button class="filter-btn" onclick="setFilter('week')">本周</button>
                            <button class="filter-btn active" onclick="setFilter('month')">本月</button>
                            <button class="filter-btn" onclick="setFilter('year')">本年</button>
                            <button class="filter-btn" onclick="setFilter('all')">全部</button>
                        </div>
                    </div>
                </div>
                
                <!-- 概览卡片 -->
                <div class="overview-cards">
                    <div class="overview-card">
                        <div class="card-icon icon-games">
                            <i class="fas fa-gamepad"></i>
                        </div>
                        <div class="card-value">28</div>
                        <div class="card-label">总局数</div>
                    </div>
                    
                    <div class="overview-card">
                        <div class="card-icon icon-winrate">
                            <i class="fas fa-trophy"></i>
                        </div>
                        <div class="card-value">64%</div>
                        <div class="card-label">胜率</div>
                    </div>
                    
                    <div class="overview-card">
                        <div class="card-icon icon-score">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="card-value">+1,250</div>
                        <div class="card-label">总积分</div>
                    </div>
                    
                    <div class="overview-card">
                        <div class="card-icon icon-time">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="card-value">42h</div>
                        <div class="card-label">游戏时长</div>
                    </div>
                </div>
                
                <!-- 图表区域 -->
                <div class="chart-section">
                    <div class="section-header">
                        <div class="section-title">胜负趋势</div>
                        <a href="#" class="view-all-btn">查看详情</a>
                    </div>
                    
                    <div class="chart-card">
                        <div class="chart-title">最近7天表现</div>
                        <div class="chart-placeholder">
                            📈
                        </div>
                        <div class="chart-legend">
                            <div class="legend-item">
                                <div class="legend-color color-win"></div>
                                <span>胜 (18局)</span>
                            </div>
                            <div class="legend-item">
                                <div class="legend-color color-lose"></div>
                                <span>负 (8局)</span>
                            </div>
                            <div class="legend-item">
                                <div class="legend-color color-draw"></div>
                                <span>平 (2局)</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 最近战绩 -->
                <div class="recent-games">
                    <div class="section-header">
                        <div class="section-title">最近战绩</div>
                        <a href="history.html" class="view-all-btn">查看全部</a>
                    </div>
                    
                    <div class="game-item">
                        <div class="game-info">
                            <div class="game-title">周末家庭局</div>
                            <div class="game-meta">
                                <span><i class="fas fa-calendar"></i> 今天</span>
                                <span><i class="fas fa-users"></i> 4人局</span>
                            </div>
                        </div>
                        <div class="game-result">
                            <div class="result-score positive">+150</div>
                            <div class="result-rank">第1名</div>
                        </div>
                    </div>
                    
                    <div class="game-item">
                        <div class="game-info">
                            <div class="game-title">朋友聚会</div>
                            <div class="game-meta">
                                <span><i class="fas fa-calendar"></i> 昨天</span>
                                <span><i class="fas fa-users"></i> 4人局</span>
                            </div>
                        </div>
                        <div class="game-result">
                            <div class="result-score negative">-80</div>
                            <div class="result-rank">第3名</div>
                        </div>
                    </div>
                    
                    <div class="game-item">
                        <div class="game-info">
                            <div class="game-title">麻将馆对局</div>
                            <div class="game-meta">
                                <span><i class="fas fa-calendar"></i> 3天前</span>
                                <span><i class="fas fa-users"></i> 4人局</span>
                            </div>
                        </div>
                        <div class="game-result">
                            <div class="result-score positive">+220</div>
                            <div class="result-rank">第1名</div>
                        </div>
                    </div>
                </div>
                
                <!-- 底部导航 -->
                <div class="bottom-nav">
                    <a href="index.html" class="nav-item">
                        <i class="fas fa-home"></i>
                        <span>首页</span>
                    </a>
                    <a href="stats.html" class="nav-item active">
                        <i class="fas fa-chart-line"></i>
                        <span>统计</span>
                    </a>
                    <a href="profile.html" class="nav-item">
                        <i class="fas fa-user"></i>
                        <span>我的</span>
                    </a>
                </div>
            </div>
            
            <!-- Home指示器 -->
            <div class="home-indicator">
                <div class="home-indicator-bar"></div>
            </div>
        </div>
    </div>
    
    <script>
        let currentFilter = 'month';
        let statsData = null;

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('=== 统计页面初始化 ===');
            loadStatsData();

            // 监听数据更新事件
            window.addEventListener('gameDataUpdated', function(event) {
                console.log('统计页面检测到游戏数据更新，重新加载统计数据', event.detail);
                loadStatsData();
            });

            // 监听额外的数据变化事件
            window.addEventListener('mahjongDataChanged', function(event) {
                console.log('统计页面检测到麻将数据变化，重新加载统计数据', event.detail);
                loadStatsData();
            });

            // 监听页面可见性变化
            document.addEventListener('visibilitychange', function() {
                if (!document.hidden) {
                    console.log('统计页面重新可见，刷新数据');
                    setTimeout(() => {
                        loadStatsData();
                    }, 100);
                }
            });

            // 监听窗口焦点变化
            window.addEventListener('focus', function() {
                console.log('统计页面重新获得焦点，刷新数据');
                setTimeout(() => {
                    loadStatsData();
                }, 100);
            });

            // 监听存储变化（当其他页面修改了数据）
            window.addEventListener('storage', function(e) {
                if (e.key === 'mahjongGameData') {
                    console.log('统计页面检测到存储变化，刷新数据');
                    setTimeout(() => {
                        loadStatsData();
                    }, 50);
                }
            });

            // 定期检查数据变化（作为备用方案）
            let lastStatsHash = '';
            setInterval(() => {
                try {
                    const gameData = gameDataManager.getGameData();
                    if (gameData && gameData.games) {
                        const statsHash = JSON.stringify(Object.values(gameData.games).map(game => ({
                            id: game.id,
                            status: game.status,
                            rounds: game.rounds.length,
                            endTime: game.endTime
                        })));

                        if (statsHash !== lastStatsHash) {
                            console.log('统计页面定期检查发现数据变化，刷新统计');
                            lastStatsHash = statsHash;
                            loadStatsData();
                        }
                    }
                } catch (error) {
                    console.error('统计页面定期检查数据时发生错误:', error);
                }
            }, 2000); // 每2秒检查一次
        });

        // 加载统计数据
        function loadStatsData() {
            const gameData = gameDataManager.getGameData();
            if (!gameData || !gameData.games) {
                showEmptyStats();
                return;
            }

            // 计算统计数据
            statsData = calculateStats(gameData);
            updateStatsDisplay();
        }

        // 计算统计数据
        function calculateStats(gameData) {
            const games = Object.values(gameData.games);
            const stats = {
                totalGames: games.length,
                totalRounds: 0,
                totalWins: 0,
                totalScore: 0,
                winRate: 0,
                avgScore: 0,
                bestScore: Number.MIN_SAFE_INTEGER,
                worstScore: Number.MAX_SAFE_INTEGER,
                gameTime: 0,
                recentGames: [],
                completedGames: 0
            };

            // 过滤已完成的游戏进行统计
            const completedGames = games.filter(game => game.status === 'completed');
            stats.completedGames = completedGames.length;

            completedGames.forEach(game => {
                stats.totalRounds += game.rounds.length;

                // 计算游戏时长
                if (game.endTime && game.startTime) {
                    const duration = new Date(game.endTime) - new Date(game.startTime);
                    stats.gameTime += duration;
                }

                // 计算玩家统计（假设当前用户是第一个玩家）
                const currentPlayer = game.players && game.players[0];
                if (currentPlayer) {
                    const playerScore = currentPlayer.totalScore || 0;
                    stats.totalScore += playerScore;

                    // 更新最佳和最差分数
                    if (playerScore > stats.bestScore) {
                        stats.bestScore = playerScore;
                    }
                    if (playerScore < stats.worstScore) {
                        stats.worstScore = playerScore;
                    }

                    // 计算胜局数
                    if (game.rounds && game.rounds.length > 0) {
                        game.rounds.forEach(round => {
                            if (round.winner === currentPlayer.name) {
                                stats.totalWins++;
                            }
                        });
                    }
                }

                // 添加到最近游戏（包括进行中的游戏）
                stats.recentGames.push({
                    name: game.name,
                    date: new Date(game.startTime),
                    rounds: game.rounds ? game.rounds.length : 0,
                    score: currentPlayer ? (currentPlayer.totalScore || 0) : 0,
                    status: game.status
                });
            });

            // 如果没有完成的游戏，重置最佳/最差分数
            if (stats.completedGames === 0) {
                stats.bestScore = 0;
                stats.worstScore = 0;
            }

            // 计算平均值和胜率
            if (stats.completedGames > 0) {
                stats.avgScore = Math.round(stats.totalScore / stats.completedGames);
            }
            if (stats.totalRounds > 0) {
                stats.winRate = Math.round((stats.totalWins / stats.totalRounds) * 100);
            }

            // 转换游戏时长为小时
            stats.gameTime = Math.round(stats.gameTime / (1000 * 60 * 60));

            // 按时间排序最近游戏（包括所有游戏）
            const allGames = games.map(game => ({
                name: game.name,
                date: new Date(game.startTime),
                rounds: game.rounds ? game.rounds.length : 0,
                score: game.players && game.players[0] ? (game.players[0].totalScore || 0) : 0,
                status: game.status
            }));

            stats.recentGames = allGames.sort((a, b) => b.date - a.date);

            console.log('计算的统计数据:', stats);
            return stats;
        }

        // 更新统计显示
        function updateStatsDisplay() {
            if (!statsData) return;

            // 更新概览卡片
            updateOverviewCards();

            // 更新详细统计
            updateDetailedStats();
        }

        // 更新概览卡片
        function updateOverviewCards() {
            const cards = document.querySelectorAll('.overview-card');
            if (cards.length >= 4 && statsData) {
                // 总局数 - 显示已完成的游戏数
                const totalGamesValue = cards[0].querySelector('.card-value');
                totalGamesValue.textContent = statsData.completedGames;

                // 更新标签为更准确的描述
                const totalGamesLabel = cards[0].querySelector('.card-label');
                totalGamesLabel.textContent = '已完成';

                // 胜率
                const winRateValue = cards[1].querySelector('.card-value');
                winRateValue.textContent = `${statsData.winRate}%`;

                // 平均积分
                const avgScoreValue = cards[2].querySelector('.card-value');
                if (statsData.avgScore > 0) {
                    avgScoreValue.textContent = `+${statsData.avgScore}`;
                    avgScoreValue.style.color = '#34C759';
                } else if (statsData.avgScore < 0) {
                    avgScoreValue.textContent = statsData.avgScore;
                    avgScoreValue.style.color = '#FF3B30';
                } else {
                    avgScoreValue.textContent = '0';
                    avgScoreValue.style.color = 'var(--text-primary)';
                }

                // 更新标签
                const avgScoreLabel = cards[2].querySelector('.card-label');
                avgScoreLabel.textContent = '平均分';

                // 游戏时长
                const gameTimeValue = cards[3].querySelector('.card-value');
                if (statsData.gameTime > 0) {
                    gameTimeValue.textContent = `${statsData.gameTime}h`;
                } else {
                    gameTimeValue.textContent = '0h';
                }

                console.log('概览卡片已更新:', {
                    completedGames: statsData.completedGames,
                    winRate: statsData.winRate,
                    avgScore: statsData.avgScore,
                    gameTime: statsData.gameTime
                });
            }
        }

        // 更新详细统计
        function updateDetailedStats() {
            // 更新胜负趋势
            updateWinLossTrend();

            // 更新最近对局
            updateRecentGames();

            console.log('详细统计数据:', statsData);
        }

        // 更新胜负趋势
        function updateWinLossTrend() {
            if (!statsData) return;

            // 更新图例数据
            const legendItems = document.querySelectorAll('.legend-item span');
            if (legendItems.length >= 3) {
                legendItems[0].textContent = `胜 (${statsData.totalWins}局)`;
                legendItems[1].textContent = `负 (${statsData.totalRounds - statsData.totalWins}局)`;
                legendItems[2].textContent = `平 (0局)`; // 暂时设为0，后续可以添加平局统计
            }

            // 更新图表标题
            const chartTitle = document.querySelector('.chart-title');
            if (chartTitle) {
                chartTitle.textContent = `最近${Math.min(7, statsData.totalGames)}场游戏表现`;
            }
        }

        // 更新最近对局
        function updateRecentGames() {
            if (!statsData || !statsData.recentGames) return;

            const recentGamesContainer = document.querySelector('.recent-games');
            if (!recentGamesContainer) return;

            // 获取最近5场游戏
            const recentGames = statsData.recentGames.slice(0, 5);

            // 清除现有的游戏项（保留标题）
            const existingItems = recentGamesContainer.querySelectorAll('.game-item');
            existingItems.forEach(item => item.remove());

            if (recentGames.length === 0) {
                // 显示空状态
                const emptyState = document.createElement('div');
                emptyState.className = 'empty-state';
                emptyState.innerHTML = `
                    <div style="text-align: center; padding: 40px 20px; color: var(--text-secondary);">
                        <i class="fas fa-gamepad" style="font-size: 48px; opacity: 0.3; margin-bottom: 16px;"></i>
                        <div style="font-size: 16px; margin-bottom: 8px;">还没有游戏记录</div>
                        <div style="font-size: 14px;">开始你的第一局麻将游戏吧</div>
                    </div>
                `;
                recentGamesContainer.appendChild(emptyState);
                return;
            }

            // 添加最近的游戏项
            recentGames.forEach((game, index) => {
                const gameItem = createGameItem(game, index);
                recentGamesContainer.appendChild(gameItem);
            });
        }

        // 创建游戏项元素
        function createGameItem(game, index) {
            const gameItem = document.createElement('div');
            gameItem.className = 'game-item';

            const timeText = formatGameTime(game.date);
            const scoreClass = game.score >= 0 ? 'positive' : 'negative';
            const scoreText = game.score >= 0 ? `+${game.score}` : game.score;
            const rankText = getRankText(game.score, index);

            gameItem.innerHTML = `
                <div class="game-info">
                    <div class="game-title">${game.name}</div>
                    <div class="game-meta">
                        <span><i class="fas fa-calendar"></i> ${timeText}</span>
                        <span><i class="fas fa-dice"></i> ${game.rounds}局</span>
                    </div>
                </div>
                <div class="game-result">
                    <div class="result-score ${scoreClass}">${scoreText}</div>
                    <div class="result-rank">${rankText}</div>
                </div>
            `;

            return gameItem;
        }

        // 格式化游戏时间
        function formatGameTime(date) {
            const now = new Date();
            const gameDate = new Date(date);
            const diffTime = now - gameDate;
            const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

            if (diffDays === 0) {
                return '今天';
            } else if (diffDays === 1) {
                return '昨天';
            } else if (diffDays < 7) {
                return `${diffDays}天前`;
            } else {
                return gameDate.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' });
            }
        }

        // 获取排名文本
        function getRankText(score, index) {
            if (score > 0) {
                return '胜利';
            } else if (score === 0) {
                return '平局';
            } else {
                return '失败';
            }
        }

        // 显示空状态
        function showEmptyStats() {
            const container = document.querySelector('.stats-container');
            if (container) {
                container.innerHTML = `
                    <div style="text-align: center; padding: 60px 20px; color: var(--text-secondary);">
                        <i class="fas fa-chart-bar" style="font-size: 64px; opacity: 0.3; margin-bottom: 20px;"></i>
                        <div style="font-size: 18px; margin-bottom: 8px;">还没有战绩数据</div>
                        <div style="font-size: 14px; margin-bottom: 24px;">开始你的第一局麻将游戏吧</div>
                        <button onclick="window.location.href='create-game.html'" style="
                            background: var(--primary-color);
                            color: white;
                            border: none;
                            border-radius: 8px;
                            padding: 12px 24px;
                            font-size: 16px;
                            cursor: pointer;
                        ">
                            <i class="fas fa-plus"></i> 创建牌局
                        </button>
                    </div>
                `;
            }
        }

        // 设置时间筛选
        function setFilter(period) {
            // 移除所有active类
            document.querySelectorAll('.filter-btn').forEach(btn => {
                btn.classList.remove('active');
            });

            // 添加active类到当前按钮
            event.target.classList.add('active');

            currentFilter = period;

            // 重新计算和显示数据
            loadStatsData();

            showToast(`已切换到${getFilterName(period)}数据`, 'success');
        }

        // 获取筛选器名称
        function getFilterName(period) {
            const names = {
                'week': '本周',
                'month': '本月',
                'year': '本年',
                'all': '全部'
            };
            return names[period] || '未知';
        }
    </script>
</body>
</html>
